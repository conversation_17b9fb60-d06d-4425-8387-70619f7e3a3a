#!/usr/bin/env python3
"""
IB数据权限问题解决方案
帮助用户理解和解决市场数据订阅问题
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_ib_permissions():
    """检查IB数据权限"""
    print("🔍 IB数据权限诊断工具")
    print("="*50)

    try:
        from trade.app.trading_app import create_app_config, TradingApplication
        import logging
        logging.getLogger().setLevel(logging.ERROR)

        print("📞 连接IB Gateway...")
        config = create_app_config(mode='paper')
        app = TradingApplication(config)
        app.initialize()

        print("✅ IB Gateway连接成功")

        # 测试股票数据权限
        print("\n📊 测试股票数据权限...")
        try:
            stock_contract = app.ib_adapter.data_provider.ib_client.get_stock_contract('SQQQ')
            if stock_contract:
                print("✅ SQQQ合约获取成功")

                # 尝试获取实时报价
                ticker = app.ib_adapter.data_provider.ib_client.get_market_data(stock_contract)
                if ticker and (ticker.last or ticker.bid or ticker.ask):
                    print("✅ 实时数据: 可用")
                    print(f"   最新价: ${ticker.last or 'N/A'}")
                    print(f"   买价: ${ticker.bid or 'N/A'}")
                    print(f"   卖价: ${ticker.ask or 'N/A'}")
                else:
                    print("⚠️  实时数据: 受限 (可能需要订阅)")
                    print("   💡 解决方案: 开通美股实时数据")
            else:
                print("❌ SQQQ合约获取失败")
        except Exception as e:
            if "10089" in str(e):
                print("❌ 实时数据: 需要付费订阅")
                print("   💡 Error 10089: 市场数据订阅不足")
            else:
                print(f"❌ 股票数据错误: {e}")

        # 测试期权权限
        print(f"\n🎯 测试期权数据权限...")
        try:
            if stock_contract:
                # 尝试获取期权链
                option_data = app.ib_adapter.data_provider.ib_client.get_option_chain(stock_contract, 30)
                if option_data and option_data.get('puts'):
                    print("✅ 期权数据: 可用")
                    print(f"   期权合约数: {len(option_data['puts'])} Put + {len(option_data['calls'])} Call")
                else:
                    print("❌ 期权数据: 不可用")
                    print("   💡 可能原因: 期权权限 或 市场收盘")
        except Exception as e:
            print(f"❌ 期权数据错误: {e}")

        app.stop()

    except Exception as e:
        print(f"❌ 连接失败: {e}")

    # 显示解决方案
    print("\n" + "="*50)
    print("💡 数据权限解决方案")
    print("="*50)

    print("\n🟢 免费方案:")
    print("1. 延时数据 (15分钟延时)")
    print("   - 适合: 盘后分析、历史回测")
    print("   - 限制: 无法实时交易")

    print("\n🟡 付费方案 (推荐):")
    print("1. 美股实时数据包:")
    print("   - 费用: $1-10/月")
    print("   - 包含: NASDAQ, NYSE实时价格")
    print("   - 适合: 日内交易、实时监控")

    print("\n2. 期权数据包:")
    print("   - 费用: $1-5/月")
    print("   - 包含: 期权链、Greeks")
    print("   - 适合: 期权策略")

    print("\n🔧 配置步骤:")
    print("1. 登录IB Trader Workstation")
    print("2. 账户管理 → 市场数据订阅")
    print("3. 选择: US Securities Snapshot and Futures Value Bundle")
    print("4. 重启IB Gateway")

    print("\n⚡ 立即可用的替代方案:")
    print("1. 使用历史数据制定策略")
    print("2. 盘后分析和计划")
    print("3. 基于技术指标的系统化交易")

def show_alternative_data_sources():
    """显示替代数据源"""
    print("\n🌐 免费数据源推荐:")
    print("="*30)
    print("1. Yahoo Finance API")
    print("2. Alpha Vantage (免费500次/天)")
    print("3. IEX Cloud (免费限额)")
    print("4. Quandl/Nasdaq Data Link")

    print("\n📊 策略建议:")
    print("- 用免费数据做研究和回测")
    print("- 用IB数据做实盘交易")
    print("- 结合多数据源验证信号")

def main():
    """主函数"""
    check_ib_permissions()

    choice = input("\n是否查看替代数据源? (y/n): ")
    if choice.lower() == 'y':
        show_alternative_data_sources()

    print("\n🎯 下一步建议:")
    print("1. 先用 smart_sqqq_data.py 测试")
    print("2. 考虑开通付费数据订阅")
    print("3. 专注历史数据回测验证策略")

if __name__ == "__main__":
    main()