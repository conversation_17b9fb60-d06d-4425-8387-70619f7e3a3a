#!/usr/bin/env python3
"""
SQQQ 数据快速获取工具
一键获取SQQQ最新价格、技术指标和策略信号
注意: 这是便捷工具，完整的SQQQ策略在 strategies/sqqq/ 目录
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def get_sqqq_data(days=5):
    """获取SQQQ数据"""
    try:
        from trade.app.trading_app import create_app_config, TradingApplication
        from datetime import datetime, timedelta
        import logging

        # 减少日志输出
        logging.getLogger().setLevel(logging.WARNING)

        print("🔍 正在连接IB Gateway...")

        # 创建应用
        config = create_app_config(mode='paper')
        app = TradingApplication(config)
        app.initialize()

        # 获取最近几天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        print("📊 获取SQQQ数据...")
        data = app.historical_data_manager.get_data('SQQQ', start_date, end_date)

        if not data.empty:
            latest = data.iloc[-1]
            latest_time = data.index[-1]
            prev = data.iloc[-2] if len(data) > 1 else latest

            # 价格变化
            price_change = latest['close'] - prev['close']
            price_change_pct = (price_change / prev['close']) * 100

            print("\n" + "="*50)
            print(f"📈 SQQQ 实时数据 ({latest_time.strftime('%m-%d %H:%M')})")
            print("="*50)

            # 价格信息
            print(f"💰 当前价格: ${latest['close']:.2f} ", end="")
            if price_change > 0:
                print(f"📈 +${price_change:.2f} (+{price_change_pct:.1f}%)")
            elif price_change < 0:
                print(f"📉 ${price_change:.2f} ({price_change_pct:.1f}%)")
            else:
                print("➡️  持平")

            print(f"🎯 今日区间: ${latest['low']:.2f} - ${latest['high']:.2f}")
            print(f"📦 成交量:   {latest['volume']:,}")

            # 技术指标
            if 'rsi_14' in latest:
                rsi = latest['rsi_14']
                print(f"\n🔍 技术指标:")
                print(f"   📊 RSI(14): {rsi:.1f}", end="")
                if rsi < 30:
                    print(" 🔴 超卖区域")
                elif rsi > 70:
                    print(" 🟡 超买区域")
                elif rsi < 40:
                    print(" 🟠 偏弱")
                elif rsi > 60:
                    print(" 🟢 偏强")
                else:
                    print(" ⚪ 中性区域")

            if 'sma_20' in latest:
                sma = latest['sma_20']
                trend = "📈 上升趋势" if latest['close'] > sma else "📉 下降趋势"
                print(f"   📉 SMA(20): ${sma:.2f} ({trend})")

            # 策略信号
            if 'rsi_14' in latest and 'sma_20' in latest:
                rsi = latest['rsi_14']
                sma = latest['sma_20']
                price = latest['close']

                print(f"\n🎯 SQQQ三引擎策略:")
                print("-" * 30)

                # 引擎一：建仓
                if rsi < 30 and price < sma:
                    print("🔥 引擎一 [建仓]: 🟢 激活")
                    print("   💡 RSI超卖 + 价格低于SMA")
                    print("   📋 建议: 卖出Put期权收取权利金")
                else:
                    print("⏸️  引擎一 [建仓]: 🔴 待机")

                # 引擎二：成本湮灭
                if rsi > 70:
                    print("⚡ 引擎二 [成本湮灭]: 🟢 激活")
                    print("   💡 RSI超买，适合卖Call")
                    print("   📋 建议: 卖出Covered Call降成本")
                else:
                    print("⏸️  引擎二 [成本湮灭]: 🔴 待机")

                # 引擎三：风控
                print("🛡️  引擎三 [风控]: 🟢 监控中")
                print("   📊 止损设置: 10%")

                # 总体建议
                print(f"\n🎪 当前状态: ", end="")
                if rsi < 30 and price < sma:
                    print("🔥 绝佳建仓机会!")
                elif rsi > 70:
                    print("⚡ 成本湮灭时机!")
                elif 30 <= rsi <= 70:
                    print("⏳ 耐心等待入场")
                else:
                    print("🔍 持续观察")

            # 近期表现
            if len(data) >= 5:
                recent_data = data.tail(5)
                returns = recent_data['close'].pct_change().dropna()
                avg_return = returns.mean() * 100
                volatility = returns.std() * 100

                print(f"\n📊 近期表现 (5天):")
                print(f"   📈 平均日收益: {avg_return:.1f}%")
                print(f"   📊 波动率: {volatility:.1f}%")

            # 获取期权推荐
            print(f"\n🎯 期权策略推荐:")
            print("-" * 30)
            try:
                stock_contract = app.ib_adapter.data_provider.ib_client.get_stock_contract('SQQQ')
                if stock_contract:
                    target_options = app.ib_adapter.data_provider.ib_client.get_target_delta_options(
                        stock_contract, target_delta_put=-0.3, target_delta_call=0.3, expiration_days=30
                    )

                    if target_options and target_options.get('target_put'):
                        put = target_options['target_put']
                        put_premium = (put['bid'] + put['ask']) / 2 if put['bid'] and put['ask'] else 0
                        print(f"📉 建仓Put: ${put['strike']:.2f} @ ${put_premium:.2f} (Δ={put['delta']:.2f})")

                    if target_options and target_options.get('target_call'):
                        call = target_options['target_call']
                        call_premium = (call['bid'] + call['ask']) / 2 if call['bid'] and call['ask'] else 0
                        print(f"📈 湮灭Call: ${call['strike']:.2f} @ ${call_premium:.2f} (Δ={call['delta']:.2f})")

                    print(f"💡 详细期权: python get_sqqq_options.py")
                else:
                    print("⚠️  期权数据获取失败")
            except Exception as e:
                print(f"⚠️  期权数据: {str(e)[:50]}...")

        else:
            print("❌ 无法获取SQQQ数据")
            print("💡 请检查:")
            print("   1. IB Gateway是否运行")
            print("   2. API是否已启用")
            print("   3. SQQQ数据权限")

        app.stop()

    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("\n🔧 故障排除:")
        print("1. 确保 IB Gateway 正在运行")
        print("2. 检查 API 设置已启用")
        print("3. 确认端口 7497 (Paper) 或 7496 (Live)")
        print("4. 检查防火墙设置")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        try:
            days = int(sys.argv[1])
            get_sqqq_data(days)
        except ValueError:
            print("用法: python get_sqqq.py [天数]")
            print("示例: python get_sqqq.py 10")
    else:
        get_sqqq_data()

if __name__ == "__main__":
    main()