#!/usr/bin/env python3
"""
企业级交易执行系统使用示例
展示如何使用重构后的专业量化交易系统
"""
import sys
import time
import signal
from datetime import datetime, timezone

# 添加项目路径
sys.path.insert(0, '.')

# 导入企业级交易系统
from trade.execution import TradingSystem, TradingSystemConfig
from trade.core.base import OrderSide, OrderType, TimeInForce
from trade.monitoring import setup_trading_logging, get_trading_logger, add_alert_handler

# 设置日志系统
setup_trading_logging(log_level="INFO")
logger = get_trading_logger(__name__, "TradingExample")


class TradingExample:
    """交易示例类"""
    
    def __init__(self):
        self.trading_system = None
        self.running = True
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"🛑 收到信号 {signum}，正在优雅关闭...")
        self.running = False
        if self.trading_system:
            self.trading_system.stop()
    
    def setup_alert_handlers(self):
        """设置警报处理器"""
        def email_alert_handler(alert, resolved=False):
            """邮件警报处理器（示例）"""
            if resolved:
                logger.info(f"📧 警报已解决: {alert['message']}")
            else:
                logger.warning(f"📧 发送警报邮件: {alert['message']}")
        
        def slack_alert_handler(alert, resolved=False):
            """Slack警报处理器（示例）"""
            if resolved:
                logger.info(f"💬 Slack通知: 警报已解决 - {alert['message']}")
            else:
                logger.warning(f"💬 Slack通知: 新警报 - {alert['message']}")
        
        # 添加警报处理器
        add_alert_handler(email_alert_handler)
        add_alert_handler(slack_alert_handler)
        
        logger.info("📢 警报处理器设置完成")
    
    def create_trading_system(self):
        """创建交易系统"""
        logger.info("🏗️ 创建企业级交易执行系统...")
        
        # 系统配置
        config = TradingSystemConfig(
            # IB连接配置
            ib_host="127.0.0.1",
            ib_port=7497,
            ib_client_id=100,  # 使用专用客户端ID
            
            # 风险管理配置
            max_position_size=1000.0,      # 最大持仓规模
            max_daily_loss=10000.0,        # 最大日损失
            max_order_size=500.0,          # 最大单笔订单
            
            # 系统配置
            enable_risk_management=True,    # 启用风险管理
            enable_monitoring=True,         # 启用监控
            log_level="INFO"
        )
        
        # 创建交易系统
        self.trading_system = TradingSystem(config)
        logger.info("✅ 交易系统创建完成")
    
    def demonstrate_trading_workflow(self):
        """演示交易工作流程"""
        logger.info("🎯 演示交易工作流程...")
        
        try:
            # 1. 获取系统状态
            logger.info("📊 获取系统状态...")
            status = self.trading_system.get_system_status()
            
            logger.info(f"   系统运行: {status['system_running']}")
            logger.info(f"   网关连接: {status['gateway_connected']}")
            logger.info(f"   活跃订单: {status.get('order_management', {}).get('active_orders_count', 0)}")
            
            # 2. 获取当前持仓
            logger.info("📊 获取当前持仓...")
            positions = self.trading_system.get_positions()
            logger.info(f"   当前持仓数量: {len(positions)}")
            
            for position in positions[:3]:  # 显示前3个持仓
                logger.info(f"   {position.symbol}: {position.quantity}@{position.avg_cost:.4f}")
            
            # 3. 演示订单创建（模拟）
            logger.info("📋 演示订单创建...")
            
            # 注意：这里只是演示，不会实际提交订单
            # 实际使用时请确保有足够资金和风险控制
            
            sample_orders = [
                {
                    'symbol': 'AAPL',
                    'side': OrderSide.BUY,
                    'quantity': 10.0,
                    'order_type': OrderType.LIMIT,
                    'price': 150.0,
                    'description': '苹果股票限价买单'
                },
                {
                    'symbol': 'EURUSD',
                    'side': OrderSide.SELL,
                    'quantity': 1000.0,
                    'order_type': OrderType.MARKET,
                    'description': '欧元美元市价卖单'
                }
            ]
            
            for order_info in sample_orders:
                logger.info(f"   模拟订单: {order_info['description']}")
                logger.info(f"      {order_info['symbol']} {order_info['side'].value} {order_info['quantity']}")
                
                # 实际提交订单的代码（注释掉以避免意外交易）
                # order_id = self.trading_system.submit_order(
                #     symbol=order_info['symbol'],
                #     side=order_info['side'],
                #     quantity=order_info['quantity'],
                #     order_type=order_info['order_type'],
                #     price=order_info.get('price'),
                #     time_in_force=TimeInForce.DAY
                # )
                # 
                # if order_id:
                #     logger.info(f"✅ 订单提交成功: {order_id}")
                # else:
                #     logger.error("❌ 订单提交失败")
            
            # 4. 获取订单历史
            logger.info("📋 获取订单历史...")
            orders = self.trading_system.get_orders()
            logger.info(f"   历史订单数量: {len(orders)}")
            
            # 5. 监控系统指标
            logger.info("📈 监控系统指标...")
            from trade.monitoring import get_monitoring_metrics
            
            metrics = get_monitoring_metrics()
            if 'log_statistics' in metrics:
                log_stats = metrics['log_statistics']
                logger.info(f"   日志统计: INFO={log_stats.get('INFO_total', 0)}, "
                          f"WARNING={log_stats.get('WARNING_total', 0)}, "
                          f"ERROR={log_stats.get('ERROR_total', 0)}")
            
            logger.info("✅ 交易工作流程演示完成")
            
        except Exception as e:
            logger.error(f"❌ 交易工作流程演示失败: {e}")
            import traceback
            traceback.print_exc()
    
    def run_monitoring_loop(self):
        """运行监控循环"""
        logger.info("🔄 启动监控循环...")
        
        monitor_count = 0
        
        while self.running:
            try:
                monitor_count += 1
                
                # 每30秒输出一次状态
                if monitor_count % 6 == 0:  # 30秒 / 5秒间隔 = 6次
                    logger.info("📊 定期状态检查...")
                    
                    status = self.trading_system.get_system_status()
                    logger.info(f"   系统运行: {status['system_running']}")
                    logger.info(f"   网关状态: {status['gateway_connected']}")
                    
                    # 检查活跃警报
                    from trade.monitoring import get_monitoring_metrics
                    metrics = get_monitoring_metrics()
                    
                    if 'active_alerts' in metrics and metrics['active_alerts']:
                        logger.warning(f"🚨 活跃警报数量: {len(metrics['active_alerts'])}")
                
                # 等待5秒
                time.sleep(5)
                
            except KeyboardInterrupt:
                logger.info("⌨️ 收到键盘中断，退出监控循环")
                break
            except Exception as e:
                logger.error(f"❌ 监控循环错误: {e}")
                time.sleep(5)
    
    def run(self):
        """运行交易示例"""
        logger.info("🚀 启动企业级交易执行系统示例")
        logger.info("=" * 60)
        
        try:
            # 1. 设置警报处理器
            self.setup_alert_handlers()
            
            # 2. 创建交易系统
            self.create_trading_system()
            
            # 3. 启动交易系统
            logger.info("🟢 启动交易系统...")
            success = self.trading_system.start()
            
            if not success:
                logger.error("❌ 交易系统启动失败")
                return False
            
            logger.info("✅ 交易系统启动成功")
            
            # 4. 等待连接稳定
            logger.info("⏳ 等待连接稳定...")
            time.sleep(3)
            
            # 5. 演示交易工作流程
            self.demonstrate_trading_workflow()
            
            # 6. 运行监控循环
            logger.info("🔄 进入监控模式（按Ctrl+C退出）...")
            self.run_monitoring_loop()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 运行交易示例失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # 确保系统正确关闭
            if self.trading_system:
                logger.info("🔴 关闭交易系统...")
                self.trading_system.stop()
            
            logger.info("👋 交易示例结束")


def main():
    """主函数"""
    logger.info("🎯 企业级量化交易执行系统示例")
    logger.info("=" * 60)
    logger.info("⚠️ 注意事项:")
    logger.info("   1. 请确保IB Gateway或TWS正在运行")
    logger.info("   2. 请确保API连接已启用")
    logger.info("   3. 本示例不会实际提交订单")
    logger.info("   4. 实际交易前请充分测试和风险评估")
    logger.info("=" * 60)
    
    # 创建并运行示例
    example = TradingExample()
    success = example.run()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
