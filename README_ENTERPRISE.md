# 企业级量化交易执行系统

基于 Interactive Brokers 的专业量化交易基础设施，提供可靠、准确、高效的交易执行能力。

## 系统特点

- **企业级架构**: 5大核心模块，分层清晰，职责分离
- **专业交易执行**: 完整的订单管理系统(OMS)，支持复杂订单类型
- **多维度风险管理**: 交易前后风控，可配置规则引擎
- **精确持仓管理**: 实时持仓跟踪，精确盈亏计算
- **实时监控系统**: 结构化日志，异常警报，性能指标
- **生产级稳定性**: 自动重连，流量控制，错误恢复

## 系统架构

```
企业级交易执行系统
├── 1. 交易接口层 (Gateway Layer)
│   ├── 连接管理 (Connection Management)
│   ├── 协议封装 (Protocol Abstraction)
│   ├── 流量控制 (Rate Limiting)
│   └── 错误处理 (Error Handling)
├── 2. 订单管理系统 (OMS)
│   ├── 订单状态机 (Order State Machine)
│   ├── 生命周期管理 (Lifecycle Management)
│   ├── 订单路由 (Order Routing)
│   └── 执行质量监控 (Execution Quality)
├── 3. 风险管理模块 (Risk Management)
│   ├── 交易前风控 (Pre-trade Risk)
│   ├── 交易后监控 (Post-trade Monitoring)
│   ├── 规则引擎 (Rule Engine)
│   └── 风险指标 (Risk Metrics)
├── 4. 持仓资金管理 (Portfolio Management)
│   ├── 持仓跟踪 (Position Tracking)
│   ├── 资金管理 (Fund Management)
│   ├── 盈亏计算 (P&L Calculation)
│   └── 成本分析 (Cost Analysis)
└── 5. 日志监控系统 (Monitoring System)
    ├── 结构化日志 (Structured Logging)
    ├── 实时监控 (Real-time Monitoring)
    ├── 异常警报 (Alert System)
    └── 性能指标 (Performance Metrics)
```

## 快速开始

### 环境要求

- Python 3.8+
- Interactive Brokers TWS 或 Gateway
- 必要的 Python 包（见 requirements.txt）

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd ib

# 安装依赖
pip install -r requirements.txt
```

### 配置 Interactive Brokers

1. 启动 IB Gateway 或 TWS
2. 启用 API 连接 (Configure -> API -> Settings)
3. 设置端口（默认 7497 用于纸交易，7496 用于实盘）
4. 确保客户端 ID 不冲突
5. 启用 "Download open orders on connection"

### 基础使用

```python
from trade.execution import TradingSystem, TradingSystemConfig
from trade.core.base import OrderSide, OrderType

# 创建系统配置
config = TradingSystemConfig(
    ib_host="127.0.0.1",
    ib_port=7497,
    ib_client_id=1,
    enable_risk_management=True,
    enable_monitoring=True
)

# 使用交易系统
with TradingSystem(config) as system:
    # 提交订单
    order_id = system.submit_order(
        symbol="AAPL",
        side=OrderSide.BUY,
        quantity=100,
        order_type=OrderType.LIMIT,
        price=150.0
    )
    
    # 获取持仓
    positions = system.get_positions()
    
    # 获取系统状态
    status = system.get_system_status()
```

## 测试系统

### 1. 系统集成测试

```bash
# 运行完整的系统测试
python test_enterprise_trading_system.py
```

测试覆盖：
- 模块导入测试
- 核心数据结构测试
- 订单状态机测试
- 风险管理系统测试
- 投资组合管理测试
- 监控系统测试
- 交易系统集成测试

### 2. IB连接测试

```bash
# 测试与IB的实际连接（需要IB Gateway运行）
python test_ib_connection.py
```

测试内容：
- 基础IB连接测试
- 企业级网关测试
- 交易系统IB集成测试

### 3. 使用示例

```bash
# 运行完整的使用示例
python example_enterprise_trading.py
```

## 项目结构

```
ib/
├── trade/                          # 企业级交易框架
│   ├── core/                       # 核心组件
│   │   ├── base.py                 # 基础数据结构
│   │   ├── config.py               # 配置管理
│   │   ├── execution_manager.py    # 执行管理器
│   │   ├── realtime_manager.py     # 实时数据管理
│   │   └── utils.py                # 工具函数
│   ├── gateway/                    # 交易接口层
│   │   ├── broker_gateway.py       # 券商网关抽象
│   │   └── ib_gateway.py           # IB网关实现
│   ├── oms/                        # 订单管理系统
│   │   └── order_manager.py        # 订单管理器
│   ├── risk/                       # 风险管理模块
│   │   └── risk_manager.py         # 风险管理器
│   ├── portfolio/                  # 持仓资金管理
│   │   └── portfolio_manager.py    # 投资组合管理器
│   ├── monitoring/                 # 日志监控系统
│   │   └── logger_config.py        # 日志配置
│   ├── execution/                  # 交易执行系统
│   │   └── trading_system.py       # 主交易系统
│   ├── adapters/                   # 兼容适配器
│   ├── data/                       # 数据管理
│   ├── engine/                     # 交易引擎
│   ├── backtest/                   # 回测引擎
│   └── app/                        # 应用程序
├── strategies/                     # 交易策略
│   └── sqqq/                       # SQQQ策略示例
├── logs/                           # 日志文件
├── results/                        # 回测结果
├── test_enterprise_trading_system.py  # 系统测试
├── test_ib_connection.py              # IB连接测试
├── example_enterprise_trading.py      # 使用示例
└── README_ENTERPRISE.md               # 本文档
```

## 核心功能

### 1. 交易接口层
- **连接管理**: 自动重连、指数退避、健康监控
- **流量控制**: API限流、请求队列、错误处理
- **协议封装**: 统一接口抽象，支持多券商扩展
- **企业级稳定性**: 生产环境就绪

### 2. 订单管理系统 (OMS)
- **订单状态机**: 完整的生命周期管理
- **订单路由**: 智能路由到最佳网关
- **执行质量监控**: 滑点分析、执行时间统计
- **复杂订单支持**: 括号订单、算法订单等

### 3. 风险管理系统
- **交易前风控**: 多维度风险检查
- **规则引擎**: 可配置的风险规则
- **实时监控**: 持仓风险、资金风险
- **多层保护**: 订单级、持仓级、账户级

### 4. 持仓资金管理
- **持仓跟踪**: 精确的成本计算
- **资金同步**: 实时资金状态
- **盈亏计算**: 已实现/未实现盈亏
- **风险指标**: 集中度、杠杆率等

### 5. 日志监控系统
- **结构化日志**: JSON格式，便于分析
- **实时警报**: 异常自动通知
- **性能监控**: API调用、响应时间
- **指标收集**: 系统健康状态

## 高级用法

### 自定义风险规则

```python
from trade.risk import RiskRule, RiskCheckResult

class CustomRiskRule(RiskRule):
    def check_order(self, order, context):
        # 自定义风险检查逻辑
        if order.quantity > 1000:
            return RiskCheckResult.REJECT, "订单数量过大"
        return RiskCheckResult.PASS, ""

# 添加到风险管理器
system.risk_manager.add_pre_trade_rule(CustomRiskRule("CustomRule"))
```

### 事件处理

```python
def on_order_filled(data):
    order = data['order']
    execution = data['execution']
    print(f"订单成交: {order.symbol} {execution.fill_quantity}@{execution.fill_price}")

# 添加事件处理器
system.order_manager.add_event_handler('order_execution', on_order_filled)
```

### 监控警报

```python
from trade.monitoring import add_alert_handler

def email_alert(alert, resolved=False):
    if not resolved:
        # 发送邮件警报
        send_email(f"交易系统警报: {alert['message']}")

add_alert_handler(email_alert)
```

## 风险提示

**重要提示**: 
- 本系统为专业交易基础设施，使用前请充分理解
- 实盘交易存在资金损失风险
- 建议先在纸交易环境中充分测试
- 使用前请进行完整的风险评估

## 技术支持

如有问题，请查看：
1. 系统测试结果
2. 日志文件 (logs/ 目录)
3. IB Gateway连接状态
4. 风险管理配置

## 许可证

MIT License
