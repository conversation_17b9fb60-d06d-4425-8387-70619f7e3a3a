#!/usr/bin/env python3
"""
简单的交易示例 - 普通用户快速接入IB进行自动化交易
展示如何使用核心交易框架进行基本的股票交易
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 确保可以导入trade模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trade.app.production_trading_app import ProductionTradingApplication, create_production_config
from trade.core.base import Strategy, Signal, MarketData
from typing import List, Dict, Any


class SimpleMAStrategy(Strategy):
    """
    简单移动平均策略示例
    当短期均线上穿长期均线时买入，下穿时卖出
    """

    def __init__(self, name: str = "Simple_MA_Strategy", config: Dict[str, Any] = None):
        super().__init__(name)

        # 策略配置
        config = config or {}
        self.symbol = config.get('symbol', 'SPY')
        self.short_ma_period = config.get('short_ma_period', 10)
        self.long_ma_period = config.get('long_ma_period', 30)
        self.position_size = config.get('position_size', 100)

        # 策略状态
        self.price_history = []
        self.max_history_size = self.long_ma_period + 10
        self.position = 0  # 当前持仓

        logging.info(f"初始化策略: {name}")
        logging.info(f"股票: {self.symbol}, 短期MA: {self.short_ma_period}, 长期MA: {self.long_ma_period}")

    def get_required_symbols(self) -> List[str]:
        """获取策略需要的股票代码"""
        return [self.symbol]

    def generate_signals(self, market_data: MarketData) -> List[Signal]:
        """生成交易信号"""
        signals = []

        try:
            # 更新价格历史
            self._update_price_history(market_data)

            # 计算移动平均
            if len(self.price_history) < self.long_ma_period:
                return signals

            short_ma = self._calculate_ma(self.short_ma_period)
            long_ma = self._calculate_ma(self.long_ma_period)
            prev_short_ma = self._calculate_ma(self.short_ma_period, offset=1)
            prev_long_ma = self._calculate_ma(self.long_ma_period, offset=1)

            # 生成交易信号
            current_price = market_data.close

            # 金叉：短期MA向上穿越长期MA，且无持仓时买入
            if (short_ma > long_ma and prev_short_ma <= prev_long_ma and self.position == 0):
                signal = Signal(
                    symbol=self.symbol,
                    signal_type='BUY',
                    strength=1.0,
                    timestamp=market_data.timestamp,
                    reason=f"金叉信号: 短期MA({short_ma:.2f}) > 长期MA({long_ma:.2f})",
                    metadata={
                        'price': current_price,
                        'quantity': self.position_size,
                        'short_ma': short_ma,
                        'long_ma': long_ma
                    }
                )
                signals.append(signal)
                logging.info(f"生成买入信号: {signal.reason}")

            # 死叉：短期MA向下穿越长期MA，且有持仓时卖出
            elif (short_ma < long_ma and prev_short_ma >= prev_long_ma and self.position > 0):
                signal = Signal(
                    symbol=self.symbol,
                    signal_type='SELL',
                    strength=1.0,
                    timestamp=market_data.timestamp,
                    reason=f"死叉信号: 短期MA({short_ma:.2f}) < 长期MA({long_ma:.2f})",
                    metadata={
                        'price': current_price,
                        'quantity': self.position,
                        'short_ma': short_ma,
                        'long_ma': long_ma
                    }
                )
                signals.append(signal)
                logging.info(f"生成卖出信号: {signal.reason}")

        except Exception as e:
            logging.error(f"信号生成失败: {e}")

        return signals

    def on_market_data(self, data: MarketData) -> None:
        """处理市场数据"""
        self._update_price_history(data)

        # 记录关键信息
        if len(self.price_history) >= self.long_ma_period:
            short_ma = self._calculate_ma(self.short_ma_period)
            long_ma = self._calculate_ma(self.long_ma_period)
            logging.debug(
                f"{self.symbol} - 价格: {data.close:.2f}, "
                f"短期MA: {short_ma:.2f}, 长期MA: {long_ma:.2f}, "
                f"持仓: {self.position}"
            )

    def on_signal(self, signal: Signal) -> None:
        """处理交易信号"""
        logging.info(f"策略接收信号: {signal.signal_type} - {signal.reason}")

        # 更新持仓状态
        if signal.signal_type == 'BUY':
            self.position += signal.metadata.get('quantity', self.position_size)
        elif signal.signal_type == 'SELL':
            self.position = 0  # 全部卖出

    def _update_price_history(self, market_data: MarketData):
        """更新价格历史"""
        self.price_history.append(market_data.close)

        # 维护历史数据大小
        if len(self.price_history) > self.max_history_size:
            self.price_history.pop(0)

    def _calculate_ma(self, period: int, offset: int = 0) -> float:
        """计算移动平均"""
        if len(self.price_history) < period + offset:
            return 0.0

        end_idx = len(self.price_history) - offset
        start_idx = end_idx - period
        return sum(self.price_history[start_idx:end_idx]) / period

    def get_strategy_state(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            'name': self.name,
            'symbol': self.symbol,
            'position': self.position,
            'is_active': self.is_active,
            'data_points': len(self.price_history)
        }


def main():
    """主函数 - 演示如何使用交易框架"""

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    print("=== 简单交易示例 ===")
    print("这个示例展示如何：")
    print("1. 连接到Interactive Brokers")
    print("2. 创建一个简单的移动平均策略")
    print("3. 获取实时数据并生成交易信号")
    print("4. 执行自动化交易")
    print()

    try:
        # 创建应用配置
        print("创建应用配置...")
        config = create_production_config('paper')  # 使用模拟交易

        # 创建交易应用
        print("初始化交易应用...")
        app = ProductionTradingApplication(config)
        app.initialize()

        # 创建策略
        print("创建移动平均策略...")
        strategy_config = {
            'symbol': 'SPY',      # 交易标的
            'short_ma_period': 5, # 短期均线周期
            'long_ma_period': 15, # 长期均线周期
            'position_size': 100  # 每次交易股数
        }

        strategy = SimpleMAStrategy("MA_Strategy", strategy_config)

        # 添加策略到应用
        print("添加策略到交易引擎...")
        app.add_strategy(strategy)

        print("\\n=== 系统状态 ===")
        print("交易模式: 模拟交易 (Paper Trading)")
        print("连接状态: 已连接到IB Gateway")
        print("策略状态: 移动平均策略已激活")
        print("监控股票: SPY")
        print()

        print("启动实时交易...")
        print("按 Ctrl+C 停止交易")
        print()

        # 启动实时交易
        app.run_live_trading()

    except KeyboardInterrupt:
        print("\\n用户停止交易")
    except Exception as e:
        print(f"\\n错误: {e}")
        print()
        print("故障排除：")
        print("1. 确保IB Gateway或TWS正在运行")
        print("2. 检查API设置是否已启用")
        print("3. 确认端口设置：Paper Trading = 7497, Live Trading = 7496")
        print("4. 检查网络连接和防火墙设置")
    finally:
        print("\\n感谢使用量化交易框架！")


if __name__ == "__main__":
    main()