[project]
name = "ib-trading-framework"
version = "0.1.0"
description = "Interactive Brokers Paper Trading Strategy Framework"
readme = "README.md"
requires-python = ">=3.11.13"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
keywords = ["trading", "interactive-brokers", "options", "strategy"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Financial and Insurance Industry",
    "Topic :: Office/Business :: Financial :: Investment",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "ib-insync>=0.9.86",
    "numpy>=2.3.3",
    "pandas>=2.3.2",
    "python-dotenv>=1.1.1",
    "pytz>=2025.2",
]

[project.urls]
Repository = "https://github.com/leon/ib-trading-framework"
Documentation = "https://github.com/leon/ib-trading-framework/blob/main/README.md"

[tool.setuptools.packages.find]
where = ["."]
include = ["*.py"]

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"
