#!/usr/bin/env python3
"""
智能SQQQ数据获取工具
专业量化交易版本 - 解决实际使用中的问题
"""

import sys
import os
import pytz
from datetime import datetime, timedelta
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def is_market_hours():
    """检查是否在交易时间"""
    ny_tz = pytz.timezone('America/New_York')
    ny_time = datetime.now(ny_tz)

    # 检查是否为工作日
    if ny_time.weekday() >= 5:  # 周末
        return False

    # 检查是否在交易时间 (9:30 AM - 4:00 PM ET)
    market_open = ny_time.replace(hour=9, minute=30, second=0, microsecond=0)
    market_close = ny_time.replace(hour=16, minute=0, second=0, microsecond=0)

    return market_open <= ny_time <= market_close

def get_market_status():
    """获取市场状态信息"""
    ny_tz = pytz.timezone('America/New_York')
    ny_time = datetime.now(ny_tz)

    status = {
        'current_time': ny_time,
        'is_open': is_market_hours(),
        'is_weekend': ny_time.weekday() >= 5,
        'next_open': None,
        'last_close': None
    }

    # 计算下次开盘时间
    if ny_time.weekday() >= 5:  # 周末
        days_until_monday = 7 - ny_time.weekday()
        next_monday = ny_time + timedelta(days=days_until_monday)
        status['next_open'] = next_monday.replace(hour=9, minute=30, second=0, microsecond=0)
    elif ny_time.hour < 9 or (ny_time.hour == 9 and ny_time.minute < 30):
        # 当天开盘前
        status['next_open'] = ny_time.replace(hour=9, minute=30, second=0, microsecond=0)
    elif ny_time.hour >= 16:
        # 当天收盘后
        tomorrow = ny_time + timedelta(days=1)
        if tomorrow.weekday() < 5:  # 明天不是周末
            status['next_open'] = tomorrow.replace(hour=9, minute=30, second=0, microsecond=0)
        else:  # 明天是周末
            days_until_monday = 7 - tomorrow.weekday()
            next_monday = tomorrow + timedelta(days=days_until_monday)
            status['next_open'] = next_monday.replace(hour=9, minute=30, second=0, microsecond=0)

    # 计算最近收盘时间
    if ny_time.hour >= 16:  # 今天已收盘
        status['last_close'] = ny_time.replace(hour=16, minute=0, second=0, microsecond=0)
    else:  # 今天还未收盘，取昨天收盘
        yesterday = ny_time - timedelta(days=1)
        while yesterday.weekday() >= 5:  # 跳过周末
            yesterday -= timedelta(days=1)
        status['last_close'] = yesterday.replace(hour=16, minute=0, second=0, microsecond=0)

    return status

def smart_get_sqqq_data():
    """智能获取SQQQ数据 - 根据市场状态调整策略"""
    market_status = get_market_status()

    print("🎯 智能SQQQ数据分析")
    print("="*50)
    print(f"🕒 纽约时间: {market_status['current_time'].strftime('%Y-%m-%d %H:%M:%S %Z')}")

    if market_status['is_open']:
        print("🟢 市场状态: 开盘中")
        data_strategy = "realtime"
    elif market_status['is_weekend']:
        print("🔴 市场状态: 周末休市")
        data_strategy = "last_close"
    else:
        print("🟡 市场状态: 盘后/盘前")
        data_strategy = "delayed"

    if market_status['next_open']:
        time_to_open = market_status['next_open'] - market_status['current_time']
        hours_to_open = int(time_to_open.total_seconds() / 3600)
        print(f"⏰ 下次开盘: {market_status['next_open'].strftime('%m-%d %H:%M')} ({hours_to_open}小时后)")

    try:
        from trade.app.trading_app import create_app_config, TradingApplication
        import logging

        # 设置合适的日志级别
        logging.getLogger().setLevel(logging.ERROR)  # 减少日志干扰

        print("\n🔍 连接IB Gateway (静默模式)...")

        config = create_app_config(mode='paper')
        app = TradingApplication(config)
        app.initialize()

        # 根据市场状态调整数据获取策略
        if data_strategy == "realtime":
            # 市场开盘 - 尝试获取实时数据
            days_back = 1
            print("📊 获取实时数据...")
        elif data_strategy == "delayed":
            # 盘后 - 获取当天数据
            days_back = 1
            print("📊 获取最新收盘数据...")
        else:
            # 周末 - 获取上周五数据
            days_back = 7
            print("📊 获取最近交易日数据...")

        # 获取数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)

        data = app.historical_data_manager.get_data('SQQQ', start_date, end_date)

        if not data.empty:
            latest = data.iloc[-1]
            latest_time = data.index[-1]

            # 检查数据新鲜度
            ny_tz = pytz.timezone('America/New_York')
            data_age_hours = (datetime.now(ny_tz) - latest_time.tz_convert(ny_tz)).total_seconds() / 3600

            print(f"\n📈 SQQQ数据 ({latest_time.strftime('%m-%d %H:%M')})")
            print(f"⏱️  数据年龄: {data_age_hours:.1f}小时前")
            print("="*50)

            # 价格信息
            if len(data) > 1:
                prev = data.iloc[-2]
                price_change = latest['close'] - prev['close']
                price_change_pct = (price_change / prev['close']) * 100
                change_indicator = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
                print(f"💰 价格: ${latest['close']:.2f} {change_indicator} {price_change:+.2f} ({price_change_pct:+.1f}%)")
            else:
                print(f"💰 价格: ${latest['close']:.2f}")

            print(f"📊 区间: ${latest['low']:.2f} - ${latest['high']:.2f}")
            print(f"📦 成交量: {latest['volume']:,.0f}")

            # 技术指标
            if 'rsi_14' in latest and 'sma_20' in latest:
                rsi = latest['rsi_14']
                sma = latest['sma_20']

                print(f"\n🔍 技术分析:")
                print(f"   RSI(14): {rsi:.1f}", end="")
                if rsi < 30:
                    signal_status = "🔥 超卖 - 建仓机会!"
                elif rsi > 70:
                    signal_status = "⚡ 超买 - 湮灭机会!"
                else:
                    signal_status = "⏳ 中性区域"
                print(f" ({signal_status})")

                trend = "上升" if latest['close'] > sma else "下降"
                print(f"   SMA(20): ${sma:.2f} (趋势: {trend})")

                # 策略状态
                print(f"\n🎯 三引擎策略状态:")
                print("-"*30)

                # 引擎一
                if rsi < 30 and latest['close'] < sma:
                    print("🔥 引擎一: 🟢 建仓激活 (卖Put)")
                else:
                    print("🔥 引擎一: 🔴 待机")

                # 引擎二
                if rsi > 70:
                    print("⚡ 引擎二: 🟢 湮灭激活 (卖Call)")
                else:
                    print("⚡ 引擎二: 🔴 待机")

                print("🛡️  引擎三: 🟢 风控监控")

            # 数据质量提示
            print(f"\n💡 数据说明:")
            if data_strategy == "realtime" and data_age_hours < 0.5:
                print("✅ 实时数据 - 可用于实时决策")
            elif data_age_hours < 24:
                print("✅ 当日数据 - 适合盘后分析")
            else:
                print("⚠️  历史数据 - 仅供参考")

            # 期权数据尝试 (简化版)
            try_options_data(app, market_status)

        else:
            print("❌ 无法获取SQQQ数据")
            print_troubleshooting_guide()

        app.stop()

    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print_troubleshooting_guide()

def try_options_data(app, market_status):
    """尝试获取期权数据"""
    print(f"\n🎯 期权数据检查:")
    print("-"*30)

    try:
        stock_contract = app.ib_adapter.data_provider.ib_client.get_stock_contract('SQQQ')
        if not stock_contract:
            print("❌ 无法获取SQQQ合约")
            return

        # 快速检查是否有期权权限
        print("🔍 检查期权权限...")

        if market_status['is_open']:
            print("📊 市场开盘中 - 尝试获取实时期权数据")
            # 在开盘时间可以尝试获取实时期权
            option_data = app.ib_adapter.data_provider.ib_client.get_target_delta_options(
                stock_contract, expiration_days=30
            )

            if option_data and option_data.get('target_put'):
                put = option_data['target_put']
                print(f"✅ Put期权: ${put['strike']:.2f} @ ${(put['bid']+put['ask'])/2:.2f}")

            if option_data and option_data.get('target_call'):
                call = option_data['target_call']
                print(f"✅ Call期权: ${call['strike']:.2f} @ ${(call['bid']+call['ask'])/2:.2f}")
        else:
            print("🕒 市场收盘 - 期权数据不可用")
            print("💡 建议:")
            print("   1. 开盘时间再次查询期权")
            print("   2. 使用其他期权报价源")
            print("   3. 基于历史数据制定策略")

    except Exception as e:
        print(f"⚠️  期权权限: {str(e)[:50]}...")
        print("💡 解决方案:")
        print("   1. 开通期权数据订阅")
        print("   2. 使用免费延时数据")
        print("   3. 专注股票策略")

def print_troubleshooting_guide():
    """打印故障排除指南"""
    print("\n🔧 问题诊断:")
    print("1. IB Gateway是否运行?")
    print("2. API端口7497是否开放?")
    print("3. 是否有SQQQ数据权限?")
    print("4. 网络连接是否正常?")
    print("\n💰 数据订阅说明:")
    print("- 免费: 延时15分钟数据")
    print("- 付费: 实时数据 (~$1-10/月)")
    print("- 期权: 需要额外订阅")

def main():
    """主函数"""
    smart_get_sqqq_data()

if __name__ == "__main__":
    main()