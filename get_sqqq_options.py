#!/usr/bin/env python3
"""
SQQQ 期权数据获取工具
获取SQQQ期权链、目标Delta期权和策略推荐
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def display_option_data(option_data, title="期权数据"):
    """显示期权数据"""
    print(f"\n📊 {title}")
    print("="*60)

    if not option_data:
        print("❌ 无期权数据")
        return

    stock_price = option_data['stock_price']
    expiration = option_data['expiration']

    # 格式化到期日
    from datetime import datetime
    exp_date = datetime.strptime(expiration, '%Y%m%d')
    days_to_exp = (exp_date - datetime.now()).days

    print(f"🎯 标的价格: ${stock_price:.2f}")
    print(f"📅 到期日: {exp_date.strftime('%Y-%m-%d')} ({days_to_exp}天)")

    # 显示Put期权
    if option_data.get('puts'):
        print(f"\n📉 Put期权 (共{len(option_data['puts'])}个):")
        print("-"*80)
        print(f"{'行权价':>8} {'Bid':>8} {'Ask':>8} {'Last':>8} {'Vol':>6} {'Delta':>8} {'IV':>8} {'状态'}")
        print("-"*80)

        for put in sorted(option_data['puts'], key=lambda x: x['strike']):
            strike = put['strike']
            bid = put['bid'] or 0
            ask = put['ask'] or 0
            last = put['last'] or 0
            volume = put['volume'] or 0
            delta = put['delta'] or 0
            iv = put['impliedVol'] or 0

            # 判断期权状态
            if strike > stock_price:
                status = "ITM 🟢"  # In the money
            elif abs(strike - stock_price) / stock_price < 0.05:
                status = "ATM ⚪"  # At the money
            else:
                status = "OTM 🔴"  # Out of the money

            print(f"${strike:7.2f} ${bid:7.2f} ${ask:7.2f} ${last:7.2f} {volume:6} {delta:7.3f} {iv:6.1%} {status}")

    # 显示Call期权
    if option_data.get('calls'):
        print(f"\n📈 Call期权 (共{len(option_data['calls'])}个):")
        print("-"*80)
        print(f"{'行权价':>8} {'Bid':>8} {'Ask':>8} {'Last':>8} {'Vol':>6} {'Delta':>8} {'IV':>8} {'状态'}")
        print("-"*80)

        for call in sorted(option_data['calls'], key=lambda x: x['strike']):
            strike = call['strike']
            bid = call['bid'] or 0
            ask = call['ask'] or 0
            last = call['last'] or 0
            volume = call['volume'] or 0
            delta = call['delta'] or 0
            iv = call['impliedVol'] or 0

            # 判断期权状态
            if strike < stock_price:
                status = "ITM 🟢"  # In the money
            elif abs(strike - stock_price) / stock_price < 0.05:
                status = "ATM ⚪"  # At the money
            else:
                status = "OTM 🔴"  # Out of the money

            print(f"${strike:7.2f} ${bid:7.2f} ${ask:7.2f} ${last:7.2f} {volume:6} {delta:7.3f} {iv:6.1%} {status}")

def display_strategy_recommendations(target_options, current_rsi=None):
    """显示策略推荐"""
    print(f"\n🎯 SQQQ三引擎策略推荐")
    print("="*60)

    if not target_options:
        print("❌ 无法获取目标期权数据")
        return

    stock_price = target_options['stock_price']
    target_put = target_options.get('target_put')
    target_call = target_options.get('target_call')

    # 引擎一：建仓引擎
    print("🔥 引擎一 [建仓引擎] - 卖出Put策略")
    print("-"*40)
    if target_put and current_rsi and current_rsi < 30:
        put_premium = (target_put['bid'] + target_put['ask']) / 2
        put_strike = target_put['strike']
        put_delta = target_put['delta']

        print(f"✅ 激活条件: RSI({current_rsi:.1f}) < 30")
        print(f"📋 推荐Put: ${put_strike:.2f} 行权价")
        print(f"💰 期权费: ${put_premium:.2f} (Delta: {put_delta:.3f})")
        print(f"🎲 最大收益: ${put_premium:.2f} (期权费)")
        print(f"🛡️  成本基础: ${put_strike - put_premium:.2f} (如被行权)")

        # 计算概率
        prob_expire_worthless = (stock_price - put_strike) / stock_price * 100
        if prob_expire_worthless > 0:
            print(f"📊 废纸概率: ~{prob_expire_worthless:.0f}% (价格保持 > ${put_strike:.2f})")
        else:
            print(f"⚠️  当前ITM: 可能被行权")

    elif current_rsi and current_rsi >= 30:
        print(f"⏸️  待机: RSI({current_rsi:.1f}) >= 30, 等待更好入场点")
    else:
        print("⏸️  待机: 等待RSI < 30的建仓信号")

    # 引擎二：成本湮灭
    print(f"\n⚡ 引擎二 [成本湮灭] - Covered Call策略")
    print("-"*40)
    if target_call and current_rsi and current_rsi > 70:
        call_premium = (target_call['bid'] + target_call['ask']) / 2
        call_strike = target_call['strike']
        call_delta = target_call['delta']

        print(f"✅ 激活条件: RSI({current_rsi:.1f}) > 70")
        print(f"📋 推荐Call: ${call_strike:.2f} 行权价")
        print(f"💰 期权费: ${call_premium:.2f} (Delta: {call_delta:.3f})")
        print(f"📉 降低成本: ${call_premium:.2f} / 股")
        print(f"🎯 获利了结: ${call_strike:.2f} (如被行权)")

    elif current_rsi and current_rsi <= 70:
        print(f"⏸️  待机: RSI({current_rsi:.1f}) <= 70, 等待超买信号")
    else:
        print("⏸️  待机: 需要持有股票且RSI > 70")

    # 引擎三：风险管理
    print(f"\n🛡️  引擎三 [风险管理]")
    print("-"*40)
    print("🟢 监控状态: 持续监控10%止损位")
    print("📊 风控规则: 持仓亏损超过10%自动止损")
    print("⚡ 动态调整: 根据市况调整仓位大小")

def get_sqqq_options_data(expiration_days=30):
    """获取SQQQ期权数据"""
    try:
        from trade.app.trading_app import create_app_config, TradingApplication
        from datetime import datetime, timedelta
        import logging

        # 减少日志输出
        logging.getLogger().setLevel(logging.WARNING)

        print("🔍 正在连接IB Gateway...")

        # 创建应用
        config = create_app_config(mode='paper')
        app = TradingApplication(config)
        app.initialize()

        print("📊 获取SQQQ股票和期权数据...")

        # 获取股票合约
        stock_contract = app.ib_adapter.data_provider.ib_client.get_stock_contract('SQQQ')
        if not stock_contract:
            print("❌ 无法获取SQQQ合约")
            return

        # 获取股票数据（包含RSI）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        stock_data = app.historical_data_manager.get_data('SQQQ', start_date, end_date)

        current_rsi = None
        if not stock_data.empty and 'rsi_14' in stock_data.columns:
            current_rsi = stock_data['rsi_14'].iloc[-1]

        print("📈 获取期权链数据...")

        # 获取期权链数据
        option_data = app.ib_adapter.data_provider.ib_client.get_option_chain(
            stock_contract, expiration_days
        )

        if option_data:
            display_option_data(option_data, f"SQQQ期权链 ({expiration_days}天到期)")

            # 获取目标Delta期权
            print("\n🎯 获取策略目标期权...")
            target_options = app.ib_adapter.data_provider.ib_client.get_target_delta_options(
                stock_contract,
                target_delta_put=-0.3,  # 策略目标Put Delta
                target_delta_call=0.3   # 策略目标Call Delta
            )

            if target_options:
                print(f"\n🎯 策略目标期权 (Put: Δ=-0.3, Call: Δ=0.3)")
                print("="*60)

                # 显示目标Put
                if target_options['target_put']:
                    put = target_options['target_put']
                    put_mid = (put['bid'] + put['ask']) / 2 if put['bid'] and put['ask'] else 0
                    print(f"📉 目标Put:  ${put['strike']:.2f} | ${put_mid:.2f} | Δ={put['delta']:.3f}")

                # 显示目标Call
                if target_options['target_call']:
                    call = target_options['target_call']
                    call_mid = (call['bid'] + call['ask']) / 2 if call['bid'] and call['ask'] else 0
                    print(f"📈 目标Call: ${call['strike']:.2f} | ${call_mid:.2f} | Δ={call['delta']:.3f}")

                # 显示策略推荐
                display_strategy_recommendations(target_options, current_rsi)

        else:
            print("❌ 无法获取期权数据")
            print("💡 可能原因:")
            print("   1. 市场已收盘")
            print("   2. 期权数据权限不足")
            print("   3. SQQQ期权不可交易")

        app.stop()

    except Exception as e:
        print(f"❌ 获取期权数据失败: {e}")
        print("\n🔧 故障排除:")
        print("1. 确保 IB Gateway 正在运行")
        print("2. 检查期权交易权限")
        print("3. 确认SQQQ期权数据订阅")
        print("4. 检查网络连接")

def main():
    """主函数"""
    print("🎯 SQQQ期权数据分析工具")
    print("专为三引擎策略设计")

    if len(sys.argv) > 1:
        try:
            days = int(sys.argv[1])
            get_sqqq_options_data(days)
        except ValueError:
            print("用法: python get_sqqq_options.py [到期天数]")
            print("示例: python get_sqqq_options.py 45")
    else:
        # 默认30天到期
        get_sqqq_options_data(30)

if __name__ == "__main__":
    main()