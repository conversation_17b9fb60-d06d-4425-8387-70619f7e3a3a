#!/usr/bin/env python3
"""
生产级量化交易框架启动脚本
专业的broker连接和交易执行基础设施
"""

import sys
import os

# 确保当前目录在Python路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def interactive_start():
    """交互式启动 - 生产级选项"""
    print("生产级量化交易框架")
    print("=" * 50)
    print("核心功能: Broker连接 | 数据获取 | 交易执行")
    print("=" * 50)
    print("请选择运行模式:")
    print("")
    print("核心交易模式:")
    print("1. Paper Trading     - 模拟交易 (推荐)")
    print("2. Live Trading      - 实盘交易 (谨慎)")
    print("3. Backtest Analysis - 历史回测分析")
    print("")
    print("数据分析工具:")
    print("4. SQQQ 实时数据     - 获取最新数据和技术指标")
    print("5. SQQQ 期权分析     - 期权链和策略分析")
    print("6. 数据权限诊断      - IB数据权限检查")
    print("7. 智能数据获取      - 市场感知数据获取")
    print("")
    print("0. 退出")

    try:
        choice = input("\n请输入选项 (0-7): ").strip()

        if choice == '1':
            print("启动模拟交易模式...")
            from trade.app.production_trading_app import main
            sys.argv = ['run.py', 'paper']
            main()
        elif choice == '2':
            print("准备启动实盘交易...")
            confirm = input("警告: 实盘交易有风险，确认启动? (输入 'YES' 确认): ")
            if confirm == 'YES':
                from trade.app.production_trading_app import main
                sys.argv = ['run.py', 'live']
                main()
            else:
                print("已取消")
                return
        elif choice == '3':
            print("启动回测分析...")
            from trade.app.production_trading_app import main
            sys.argv = ['run.py', 'backtest']
            main()
        elif choice == '4':
            print("获取SQQQ实时数据...")
            get_sqqq_data()
            return
        elif choice == '5':
            print("分析SQQQ期权数据...")
            get_sqqq_options()
            return
        elif choice == '6':
            print("运行数据权限诊断...")
            run_data_diagnostics()
            return
        elif choice == '7':
            print("运行智能数据获取...")
            run_smart_data()
            return
        elif choice == '0':
            print("感谢使用生产级量化交易框架!")
            return
        else:
            print("无效选项，请重新选择")
            return

    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n启动失败: {e}")
        import traceback
        traceback.print_exc()

def get_sqqq_data():
    """获取SQQQ最新数据"""
    print("\n获取SQQQ最新数据...")
    print("=" * 40)

    try:
        from trade.app.trading_app import create_app_config, TradingApplication
        from datetime import datetime, timedelta

        # 创建应用
        config = create_app_config(mode='paper')
        app = TradingApplication(config)
        app.initialize()

        print("已连接到IB Gateway")

        # 获取历史数据（最近5天，包含最新数据）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=5)

        print("获取SQQQ数据中...")
        data = app.historical_data_manager.get_data('SQQQ', start_date, end_date)

        if not data.empty:
            latest = data.iloc[-1]
            latest_time = data.index[-1]

            print(f"\nSQQQ 最新数据 ({latest_time.strftime('%Y-%m-%d %H:%M')})")
            print("-" * 40)
            print(f"当前价格: ${latest['close']:.2f}")
            print(f"开盘价:   ${latest['open']:.2f}")
            print(f"最高价:   ${latest['high']:.2f}")
            print(f"最低价:   ${latest['low']:.2f}")
            print(f"成交量:   {latest['volume']:,}")

            if 'rsi_14' in latest:
                rsi = latest['rsi_14']
                print(f"\n技术指标:")
                print(f"   RSI(14): {rsi:.2f}", end="")
                if rsi < 30:
                    print(" (超卖)")
                elif rsi > 70:
                    print(" (超买)")
                else:
                    print(" (正常)")

            if 'sma_20' in latest:
                sma = latest['sma_20']
                print(f"   SMA(20): ${sma:.2f}", end="")
                if latest['close'] < sma:
                    print(" (价格低于均线 📉)")
                else:
                    print(" (价格高于均线 📈)")

            # 策略信号检查
            if 'rsi_14' in latest and 'sma_20' in latest:
                rsi = latest['rsi_14']
                sma = latest['sma_20']
                price = latest['close']

                print(f"\nSQQQ策略信号:")
                if rsi < 30 and price < sma:
                    print("   建仓信号 (引擎一): RSI超卖 + 价格低于SMA")
                    print("   建议: 考虑卖出Put期权")
                elif rsi > 70:
                    print("   成本湮灭信号 (引擎二): RSI超买")
                    print("   建议: 考虑卖出Covered Call")
                else:
                    print("   观望状态: 等待更好的进入机会")
        else:
            print("警告: 无法获取数据，请检查IB Gateway连接")

        app.stop()

    except Exception as e:
        print(f"获取数据失败: {e}")
        print("请确保:")
        print("   1. IB Gateway正在运行")
        print("   2. API连接已启用")
        print("   3. 有SQQQ的数据权限")

def get_sqqq_options():
    """获取SQQQ期权数据"""
    try:
        import subprocess
        subprocess.run([sys.executable, "get_sqqq_options.py"], check=True)
    except subprocess.CalledProcessError:
        print("运行期权工具失败")
    except FileNotFoundError:
        print("期权工具文件不存在")

def run_data_diagnostics():
    """运行数据权限诊断"""
    try:
        import subprocess
        subprocess.run([sys.executable, "fix_data_permissions.py"], check=True)
    except subprocess.CalledProcessError:
        print("运行诊断工具失败")
    except FileNotFoundError:
        print("诊断工具文件不存在")

def run_smart_data():
    """运行智能数据获取"""
    try:
        import subprocess
        subprocess.run([sys.executable, "smart_sqqq_data.py"], check=True)
    except subprocess.CalledProcessError:
        print("运行智能数据工具失败")
    except FileNotFoundError:
        print("智能数据工具文件不存在")

if __name__ == "__main__":
    # 如果有命令行参数，直接使用生产级main函数
    if len(sys.argv) > 1:
        from trade.app.production_trading_app import main
        main()
    else:
        # 否则使用交互式启动
        interactive_start()