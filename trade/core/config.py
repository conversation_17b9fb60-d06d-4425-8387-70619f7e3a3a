"""
配置管理器 - 简化配置访问和验证
"""
import os
from typing import Dict, Any
from dotenv import load_dotenv

load_dotenv()


class ConfigManager:
    """配置管理器"""

    def __init__(self):
        self._config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        return {
            # IB连接配置
            'ib': {
                'host': os.getenv('IB_HOST', '127.0.0.1'),
                'port': int(os.getenv('IB_PORT', '7497')),
                'client_id': int(os.getenv('IB_CLIENT_ID', '2')),
                'timeout': 30,
                'reconnect_attempts': 3,
                'reconnect_delay': 5
            },
            # 数据配置
            'data': {
                'default_exchange': 'SMART',
                'default_currency': 'USD',
                'history_days': 365,
                'realtime_buffer_size': 1000,
                'cache_enabled': True
            },
            # 交易配置
            'trading': {
                'default_order_type': 'MKT',
                'order_timeout': 30,
                'max_retries': 3,
                'price_tolerance': 0.01
            },
            # 系统配置
            'system': {
                'log_level': os.getenv('LOG_LEVEL', 'INFO'),
                'log_file': 'logs/trading.log',
                'max_log_size': '10MB',
                'log_backup_count': 5
            }
        }

    def get(self, section: str, key: str = None):
        """获取配置值"""
        if key is None:
            return self._config.get(section, {})
        return self._config.get(section, {}).get(key)

    def update(self, section: str, key: str, value: Any):
        """更新配置值"""
        if section not in self._config:
            self._config[section] = {}
        self._config[section][key] = value

    def validate_connection_config(self) -> bool:
        """验证连接配置"""
        ib_config = self.get('ib')
        required_keys = ['host', 'port', 'client_id']
        return all(key in ib_config for key in required_keys)

    def validate_config(self) -> tuple[bool, list]:
        """验证配置完整性"""
        errors = []

        # 验证IB配置
        if not self.validate_connection_config():
            errors.append("IB连接配置缺失必要参数")

        # 验证系统配置
        system_config = self.get('system')
        if not system_config.get('log_level') in ['DEBUG', 'INFO', 'WARNING', 'ERROR']:
            errors.append("无效的日志级别")

        return len(errors) == 0, errors


# 全局配置实例
config = ConfigManager()