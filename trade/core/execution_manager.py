"""
交易执行管理器 - 生产级订单执行和状态跟踪
专为量化交易框架设计，提供可靠的订单执行和风险控制
"""
import logging
import threading
import time
from typing import Dict, List, Optional, Callable
from datetime import datetime, timezone
from enum import Enum
from dataclasses import dataclass, field
import uuid

from .base import Order, OrderStatus, OrderType, OrderSide, Position, Event, EventType

logger = logging.getLogger(__name__)


class ExecutionStatus(Enum):
    """执行状态"""
    PENDING = "pending"           # 待执行
    SUBMITTED = "submitted"       # 已提交
    PARTIALLY_FILLED = "partial"  # 部分成交
    FILLED = "filled"            # 完全成交
    CANCELLED = "cancelled"       # 已取消
    REJECTED = "rejected"         # 被拒绝
    EXPIRED = "expired"           # 已过期


@dataclass
class OrderExecution:
    """订单执行记录"""
    order_id: str
    original_order: Order
    broker_order_id: Optional[str] = None
    status: ExecutionStatus = ExecutionStatus.PENDING
    submitted_time: Optional[datetime] = None
    filled_quantity: int = 0
    remaining_quantity: int = 0
    avg_fill_price: float = 0.0
    total_commission: float = 0.0
    executions: List[Dict] = field(default_factory=list)
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3

    def __post_init__(self):
        self.remaining_quantity = self.original_order.quantity

    @property
    def is_complete(self) -> bool:
        """订单是否完成（不管成功失败）"""
        return self.status in [ExecutionStatus.FILLED, ExecutionStatus.CANCELLED,
                              ExecutionStatus.REJECTED, ExecutionStatus.EXPIRED]

    @property
    def is_active(self) -> bool:
        """订单是否活跃（仍在市场中）"""
        return self.status in [ExecutionStatus.SUBMITTED, ExecutionStatus.PARTIALLY_FILLED]

    def add_execution(self, quantity: int, price: float, commission: float = 0.0, execution_time: datetime = None):
        """添加成交记录"""
        if execution_time is None:
            execution_time = datetime.now(timezone.utc)

        execution = {
            'quantity': quantity,
            'price': price,
            'commission': commission,
            'time': execution_time
        }

        self.executions.append(execution)
        self.filled_quantity += quantity
        self.remaining_quantity = max(0, self.original_order.quantity - self.filled_quantity)
        self.total_commission += commission

        # 更新平均成交价
        if self.filled_quantity > 0:
            total_value = sum(ex['quantity'] * ex['price'] for ex in self.executions)
            self.avg_fill_price = total_value / self.filled_quantity

        # 更新状态
        if self.remaining_quantity == 0:
            self.status = ExecutionStatus.FILLED
        elif self.filled_quantity > 0:
            self.status = ExecutionStatus.PARTIALLY_FILLED


class OrderValidator:
    """订单验证器"""

    def __init__(self, config: Dict):
        self.config = config
        self.min_order_size = config.get('min_order_size', 1)
        self.max_order_size = config.get('max_order_size', 10000)
        self.max_order_value = config.get('max_order_value', 100000)

    def validate_order(self, order: Order, account_info: Dict, current_price: float = None) -> tuple[bool, str]:
        """验证订单"""
        try:
            # 基本验证
            if order.quantity <= 0:
                return False, "订单数量必须大于0"

            if order.quantity < self.min_order_size:
                return False, f"订单数量不能小于最小值: {self.min_order_size}"

            if order.quantity > self.max_order_size:
                return False, f"订单数量不能大于最大值: {self.max_order_size}"

            # 订单价值验证
            if current_price:
                order_value = order.quantity * current_price
                if order_value > self.max_order_value:
                    return False, f"订单价值不能超过: ${self.max_order_value:,.2f}"

            # 限价单价格验证
            if order.order_type == OrderType.LIMIT and not order.price:
                return False, "限价单必须指定价格"

            if order.order_type == OrderType.LIMIT and order.price <= 0:
                return False, "限价单价格必须大于0"

            # 资金验证
            available_funds = account_info.get('AvailableFunds', 0)
            if order.side == OrderSide.BUY and current_price:
                required_funds = order.quantity * current_price * 1.1  # 10%缓冲
                if required_funds > available_funds:
                    return False, f"资金不足: 需要${required_funds:,.2f}, 可用${available_funds:,.2f}"

            return True, "验证通过"

        except Exception as e:
            return False, f"验证失败: {e}"


class ExecutionMonitor:
    """执行监控器"""

    def __init__(self):
        self.execution_stats = {
            'orders_submitted': 0,
            'orders_filled': 0,
            'orders_cancelled': 0,
            'orders_rejected': 0,
            'total_volume': 0,
            'total_commission': 0.0,
            'avg_fill_time': 0.0
        }
        self.fill_times = []

    def record_submission(self, execution: OrderExecution):
        """记录订单提交"""
        self.execution_stats['orders_submitted'] += 1

    def record_fill(self, execution: OrderExecution):
        """记录订单成交"""
        if execution.status == ExecutionStatus.FILLED:
            self.execution_stats['orders_filled'] += 1
            self.execution_stats['total_volume'] += execution.filled_quantity
            self.execution_stats['total_commission'] += execution.total_commission

            # 计算成交时间
            if execution.submitted_time:
                fill_time = (datetime.now(timezone.utc) - execution.submitted_time).total_seconds()
                self.fill_times.append(fill_time)

                # 更新平均成交时间
                if self.fill_times:
                    self.execution_stats['avg_fill_time'] = sum(self.fill_times) / len(self.fill_times)

    def record_cancellation(self, execution: OrderExecution):
        """记录订单取消"""
        self.execution_stats['orders_cancelled'] += 1

    def record_rejection(self, execution: OrderExecution):
        """记录订单拒绝"""
        self.execution_stats['orders_rejected'] += 1

    def get_stats(self) -> Dict:
        """获取执行统计"""
        total_orders = self.execution_stats['orders_submitted']
        fill_rate = (self.execution_stats['orders_filled'] / total_orders * 100) if total_orders > 0 else 0

        return {
            **self.execution_stats,
            'fill_rate_percent': fill_rate,
            'recent_fill_times': self.fill_times[-10:] if self.fill_times else []
        }


class ExecutionManager:
    """交易执行管理器 - 生产级实现"""

    def __init__(self, broker, config: Dict = None):
        self.broker = broker
        self.config = config or {}

        # 核心组件
        self.validator = OrderValidator(self.config)
        self.monitor = ExecutionMonitor()

        # 执行状态
        self.executions: Dict[str, OrderExecution] = {}
        self.active_orders: Dict[str, OrderExecution] = {}

        # 事件处理
        self.event_handlers: List[Callable] = []

        # 线程控制
        self._lock = threading.Lock()
        self.is_running = False
        self.monitor_thread = None

        # 性能配置
        self.status_check_interval = 1.0  # 秒
        self.order_timeout = 300  # 5分钟订单超时

    def add_event_handler(self, handler: Callable):
        """添加事件处理器"""
        self.event_handlers.append(handler)

    def start(self):
        """启动执行管理器"""
        if self.is_running:
            return

        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
        self.monitor_thread.start()

        logger.info("🚀 交易执行管理器已启动")

    def stop(self):
        """停止执行管理器"""
        self.is_running = False

        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        logger.info("⏹️ 交易执行管理器已停止")

    def submit_order(self, order: Order) -> tuple[bool, str, Optional[str]]:
        """提交订单"""
        try:
            # 生成订单ID
            order_id = str(uuid.uuid4())

            # 获取账户信息用于验证
            account_info = self.broker.get_account_info()

            # 获取当前价格用于验证
            try:
                current_price = self._get_current_price(order.symbol)
            except:
                current_price = order.price  # 使用订单价格作为备选

            # 验证订单
            is_valid, message = self.validator.validate_order(order, account_info, current_price)
            if not is_valid:
                logger.warning(f"❌ 订单验证失败: {message}")
                return False, message, None

            # 创建执行记录
            execution = OrderExecution(
                order_id=order_id,
                original_order=order
            )

            with self._lock:
                self.executions[order_id] = execution

            # 提交到券商
            try:
                broker_order_id = self.broker.submit_order(order)

                execution.broker_order_id = broker_order_id
                execution.status = ExecutionStatus.SUBMITTED
                execution.submitted_time = datetime.now(timezone.utc)

                with self._lock:
                    self.active_orders[order_id] = execution

                self.monitor.record_submission(execution)
                self._trigger_event(EventType.ORDER, execution)

                logger.info(f"✅ 订单已提交: {order_id} -> {broker_order_id}")
                return True, "订单提交成功", order_id

            except Exception as e:
                execution.status = ExecutionStatus.REJECTED
                execution.error_message = str(e)
                self.monitor.record_rejection(execution)

                logger.error(f"❌ 订单提交失败: {e}")
                return False, f"提交失败: {e}", order_id

        except Exception as e:
            logger.error(f"❌ 订单处理异常: {e}")
            return False, f"处理异常: {e}", None

    def cancel_order(self, order_id: str) -> tuple[bool, str]:
        """取消订单"""
        with self._lock:
            execution = self.executions.get(order_id)

        if not execution:
            return False, "订单不存在"

        if not execution.is_active:
            return False, f"订单状态不允许取消: {execution.status.value}"

        try:
            if execution.broker_order_id:
                success = self.broker.cancel_order(execution.broker_order_id)

                if success:
                    execution.status = ExecutionStatus.CANCELLED

                    with self._lock:
                        if order_id in self.active_orders:
                            del self.active_orders[order_id]

                    self.monitor.record_cancellation(execution)
                    self._trigger_event(EventType.ORDER, execution)

                    logger.info(f"✅ 订单已取消: {order_id}")
                    return True, "订单取消成功"
                else:
                    return False, "券商取消失败"
            else:
                return False, "无券商订单ID"

        except Exception as e:
            logger.error(f"❌ 取消订单失败: {e}")
            return False, f"取消失败: {e}"

    def get_order_status(self, order_id: str) -> Optional[OrderExecution]:
        """获取订单状态"""
        return self.executions.get(order_id)

    def get_active_orders(self) -> List[OrderExecution]:
        """获取活跃订单"""
        with self._lock:
            return list(self.active_orders.values())

    def get_execution_history(self, symbol: str = None, limit: int = 100) -> List[OrderExecution]:
        """获取执行历史"""
        executions = list(self.executions.values())

        if symbol:
            executions = [ex for ex in executions if ex.original_order.symbol == symbol]

        # 按时间排序
        executions.sort(key=lambda x: x.submitted_time or datetime.min.replace(tzinfo=timezone.utc), reverse=True)

        return executions[:limit]

    def _monitor_worker(self):
        """监控工作线程"""
        logger.info("🔄 执行监控线程启动")

        while self.is_running:
            try:
                self._check_order_status()
                self._check_timeouts()
                time.sleep(self.status_check_interval)

            except Exception as e:
                logger.error(f"❌ 监控线程错误: {e}")
                time.sleep(self.status_check_interval * 2)

    def _check_order_status(self):
        """检查订单状态"""
        with self._lock:
            active_orders = list(self.active_orders.items())

        for order_id, execution in active_orders:
            try:
                # 这里需要从券商获取最新状态
                # 由于IB API的复杂性，这里简化处理
                # 在实际生产中需要监听IB的状态更新事件
                pass

            except Exception as e:
                logger.error(f"❌ 检查订单状态失败 {order_id}: {e}")

    def _check_timeouts(self):
        """检查超时订单"""
        current_time = datetime.now(timezone.utc)

        with self._lock:
            timeout_orders = []

            for order_id, execution in self.active_orders.items():
                if execution.submitted_time:
                    elapsed = (current_time - execution.submitted_time).total_seconds()
                    if elapsed > self.order_timeout:
                        timeout_orders.append(order_id)

        # 处理超时订单
        for order_id in timeout_orders:
            logger.warning(f"⏰ 订单超时，尝试取消: {order_id}")
            self.cancel_order(order_id)

    def _get_current_price(self, symbol: str) -> float:
        """获取当前价格"""
        # 这里需要从数据提供者获取当前价格
        # 简化实现
        return 100.0

    def _trigger_event(self, event_type: EventType, data):
        """触发事件"""
        event = Event(
            type=event_type,
            timestamp=datetime.now(timezone.utc),
            data=data
        )

        for handler in self.event_handlers:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"❌ 事件处理器错误: {e}")

    def get_statistics(self) -> Dict:
        """获取执行统计"""
        with self._lock:
            active_count = len(self.active_orders)
            total_count = len(self.executions)

        stats = self.monitor.get_stats()
        stats.update({
            'active_orders': active_count,
            'total_orders': total_count,
            'is_running': self.is_running
        })

        return stats