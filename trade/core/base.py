"""
企业级量化交易执行系统 - 核心基础类
专业的交易执行基础设施，支持可靠、准确、高效的交易执行
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Dict, List, Optional, Union, Any, Callable
from enum import Enum
import pandas as pd
import uuid
import logging

logger = logging.getLogger(__name__)


class OrderStatus(Enum):
    """企业级订单状态枚举"""
    # 初始状态
    PENDING_NEW = "pending_new"           # 待创建
    PENDING_SUBMIT = "pending_submit"     # 待提交

    # 提交状态
    SUBMITTED = "submitted"               # 已提交
    PRE_SUBMITTED = "pre_submitted"       # 预提交

    # 执行状态
    PARTIALLY_FILLED = "partially_filled" # 部分成交
    FILLED = "filled"                     # 完全成交

    # 取消状态
    PENDING_CANCEL = "pending_cancel"     # 待取消
    CANCELLED = "cancelled"               # 已取消

    # 错误状态
    REJECTED = "rejected"                 # 已拒绝
    API_CANCELLED = "api_cancelled"       # API取消

    # 其他状态
    INACTIVE = "inactive"                 # 非活跃


class OrderType(Enum):
    """企业级订单类型"""
    # 基础订单类型
    MARKET = "MKT"                        # 市价单
    LIMIT = "LMT"                         # 限价单
    STOP = "STP"                          # 止损单
    STOP_LIMIT = "STP_LMT"               # 止损限价单

    # 高级订单类型
    TRAILING_STOP = "TRAIL"               # 跟踪止损
    TRAILING_STOP_LIMIT = "TRAIL_LIMIT"   # 跟踪止损限价

    # 算法订单类型
    TWAP = "TWAP"                         # 时间加权平均价格
    VWAP = "VWAP"                         # 成交量加权平均价格

    # 条件订单类型
    ONE_CANCELS_OTHER = "OCO"             # 二选一订单
    BRACKET = "BRACKET"                   # 括号订单


class OrderSide(Enum):
    """订单方向"""
    BUY = "BUY"
    SELL = "SELL"


class TimeInForce(Enum):
    """订单有效期"""
    DAY = "DAY"                           # 当日有效
    GTC = "GTC"                           # 撤销前有效
    IOC = "IOC"                           # 立即成交或取消
    FOK = "FOK"                           # 全部成交或取消
    GTD = "GTD"                           # 指定日期前有效


@dataclass
class MarketData:
    """市场数据结构"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    bid: Optional[float] = None
    ask: Optional[float] = None

    @property
    def mid_price(self) -> float:
        """中间价"""
        if self.bid and self.ask:
            return (self.bid + self.ask) / 2
        return self.close


@dataclass
class Order:
    """企业级订单结构"""
    # 基础信息
    order_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    client_order_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    symbol: str = ""
    side: OrderSide = OrderSide.BUY
    order_type: OrderType = OrderType.MARKET
    quantity: float = 0.0

    # 价格信息
    price: Optional[float] = None
    stop_price: Optional[float] = None
    aux_price: Optional[float] = None

    # 订单属性
    time_in_force: TimeInForce = TimeInForce.DAY
    good_till_date: Optional[datetime] = None

    # 状态信息
    status: OrderStatus = OrderStatus.PENDING_NEW
    created_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    submitted_time: Optional[datetime] = None
    last_update_time: Optional[datetime] = None

    # 执行信息
    filled_quantity: float = 0.0
    remaining_quantity: float = 0.0
    avg_fill_price: Optional[float] = None

    # 佣金和费用
    commission: Optional[float] = None
    commission_currency: Optional[str] = None

    # 其他信息
    parent_order_id: Optional[str] = None
    oca_group: Optional[str] = None  # One-Cancels-All group
    account: Optional[str] = None
    exchange: Optional[str] = None

    # 错误信息
    error_code: Optional[int] = None
    error_message: Optional[str] = None

    def __post_init__(self):
        """初始化后处理"""
        if self.remaining_quantity == 0.0:
            self.remaining_quantity = self.quantity

    @property
    def is_active(self) -> bool:
        """订单是否活跃"""
        return self.status in [
            OrderStatus.PENDING_NEW,
            OrderStatus.PENDING_SUBMIT,
            OrderStatus.SUBMITTED,
            OrderStatus.PRE_SUBMITTED,
            OrderStatus.PARTIALLY_FILLED
        ]

    @property
    def is_filled(self) -> bool:
        """订单是否完全成交"""
        return self.status == OrderStatus.FILLED

    @property
    def is_cancelled(self) -> bool:
        """订单是否已取消"""
        return self.status in [
            OrderStatus.CANCELLED,
            OrderStatus.API_CANCELLED
        ]

    @property
    def fill_percentage(self) -> float:
        """成交百分比"""
        if self.quantity == 0:
            return 0.0
        return (self.filled_quantity / self.quantity) * 100.0

    def update_status(self, new_status: OrderStatus, timestamp: Optional[datetime] = None):
        """更新订单状态"""
        self.status = new_status
        self.last_update_time = timestamp or datetime.now(timezone.utc)

        if new_status == OrderStatus.SUBMITTED and not self.submitted_time:
            self.submitted_time = self.last_update_time


@dataclass
class Position:
    """企业级持仓结构"""
    # 基础信息
    symbol: str = ""
    quantity: float = 0.0
    avg_cost: float = 0.0
    market_price: float = 0.0

    # 成本信息
    total_cost: float = 0.0
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0

    # 市场信息
    market_value: float = 0.0
    last_update_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    # 交易信息
    total_bought: float = 0.0
    total_sold: float = 0.0
    total_commission: float = 0.0

    # 其他信息
    currency: str = "USD"
    exchange: str = ""
    account: str = ""

    @property
    def is_long(self) -> bool:
        """是否多头持仓"""
        return self.quantity > 0

    @property
    def is_short(self) -> bool:
        """是否空头持仓"""
        return self.quantity < 0

    @property
    def is_flat(self) -> bool:
        """是否平仓"""
        return abs(self.quantity) < 1e-8

    @property
    def notional_value(self) -> float:
        """名义价值"""
        return abs(self.quantity * self.market_price)

    @property
    def total_pnl(self) -> float:
        """总盈亏"""
        return self.realized_pnl + self.unrealized_pnl

    @property
    def pnl_percentage(self) -> float:
        """盈亏百分比"""
        if self.total_cost == 0:
            return 0.0
        return (self.total_pnl / abs(self.total_cost)) * 100.0

    def update_market_price(self, new_price: float):
        """更新市场价格"""
        self.market_price = new_price
        self.market_value = self.quantity * new_price
        self.unrealized_pnl = (new_price - self.avg_cost) * self.quantity
        self.last_update_time = datetime.now(timezone.utc)

    def add_trade(self, quantity: float, price: float, commission: float = 0.0):
        """添加交易记录"""
        if quantity > 0:  # 买入
            self.total_bought += quantity
            if self.quantity >= 0:  # 增加多头或从空头减少
                # 重新计算平均成本
                total_value = self.quantity * self.avg_cost + quantity * price
                self.quantity += quantity
                self.avg_cost = total_value / self.quantity if self.quantity != 0 else 0.0
            else:  # 空头回补
                if quantity >= abs(self.quantity):  # 完全回补并转多
                    # 计算空头平仓盈亏
                    cover_quantity = abs(self.quantity)
                    self.realized_pnl += (self.avg_cost - price) * cover_quantity

                    # 剩余数量建立多头
                    remaining = quantity - cover_quantity
                    self.quantity = remaining
                    self.avg_cost = price if remaining > 0 else 0.0
                else:  # 部分回补
                    self.realized_pnl += (self.avg_cost - price) * quantity
                    self.quantity += quantity  # quantity为正，self.quantity为负
        else:  # 卖出
            sell_quantity = abs(quantity)
            self.total_sold += sell_quantity

            if self.quantity <= 0:  # 增加空头或从多头减少
                if self.quantity == 0:  # 从零开始做空
                    self.quantity = -sell_quantity
                    self.avg_cost = price
                else:  # 增加空头
                    total_value = abs(self.quantity) * self.avg_cost + sell_quantity * price
                    self.quantity -= sell_quantity
                    self.avg_cost = total_value / abs(self.quantity)
            else:  # 多头平仓
                if sell_quantity >= self.quantity:  # 完全平仓并转空
                    # 计算多头平仓盈亏
                    close_quantity = self.quantity
                    self.realized_pnl += (price - self.avg_cost) * close_quantity

                    # 剩余数量建立空头
                    remaining = sell_quantity - close_quantity
                    self.quantity = -remaining
                    self.avg_cost = price if remaining > 0 else 0.0
                else:  # 部分平仓
                    self.realized_pnl += (price - self.avg_cost) * sell_quantity
                    self.quantity -= sell_quantity

        # 更新总成本和佣金
        self.total_cost = abs(self.quantity * self.avg_cost)
        self.total_commission += commission

        # 更新市场价值和未实现盈亏
        if self.market_price > 0:
            self.update_market_price(self.market_price)


@dataclass
class Signal:
    """交易信号"""
    symbol: str
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    strength: float  # 信号强度 0-1
    timestamp: datetime
    reason: str = ""
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class EventType(Enum):
    """事件类型"""
    MARKET_DATA = "market_data"
    SIGNAL = "signal"
    ORDER = "order"
    FILL = "fill"
    POSITION_UPDATE = "position_update"


@dataclass
class Event:
    """事件基类"""
    type: EventType
    timestamp: datetime
    data: Any

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class Component(ABC):
    """框架组件基类"""

    def __init__(self, name: str):
        self.name = name
        self.is_active = False

    @abstractmethod
    def start(self) -> None:
        """启动组件"""
        pass

    @abstractmethod
    def stop(self) -> None:
        """停止组件"""
        pass

    @abstractmethod
    def process_event(self, event: Event) -> None:
        """处理事件"""
        pass


class DataProvider(ABC):
    """数据提供者接口"""

    @abstractmethod
    def get_historical_data(self, symbol: str, start_date: datetime,
                          end_date: datetime) -> pd.DataFrame:
        """获取历史数据"""
        pass

    @abstractmethod
    def get_realtime_data(self, symbol: str) -> MarketData:
        """获取实时数据"""
        pass

    @abstractmethod
    def subscribe_data(self, symbol: str, callback) -> None:
        """订阅实时数据"""
        pass


class Broker(ABC):
    """券商接口"""

    @abstractmethod
    def submit_order(self, order: Order) -> str:
        """提交订单"""
        pass

    @abstractmethod
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        pass

    @abstractmethod
    def get_positions(self) -> List[Position]:
        """获取持仓"""
        pass

    @abstractmethod
    def get_account_info(self) -> Dict[str, float]:
        """获取账户信息"""
        pass


class Strategy(ABC):
    """策略基类"""

    def __init__(self, name: str):
        self.name = name
        self.is_active = False

    @abstractmethod
    def generate_signals(self, market_data: MarketData) -> List[Signal]:
        """生成交易信号"""
        pass

    @abstractmethod
    def on_market_data(self, data: MarketData) -> None:
        """处理市场数据"""
        pass

    @abstractmethod
    def on_signal(self, signal: Signal) -> None:
        """处理交易信号"""
        pass