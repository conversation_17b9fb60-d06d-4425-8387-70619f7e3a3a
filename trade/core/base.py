"""
量化交易框架 - 核心基础类
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Union, Any
from enum import Enum
import pandas as pd


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


@dataclass
class MarketData:
    """市场数据结构"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    bid: Optional[float] = None
    ask: Optional[float] = None

    @property
    def mid_price(self) -> float:
        """中间价"""
        if self.bid and self.ask:
            return (self.bid + self.ask) / 2
        return self.close


@dataclass
class Order:
    """订单结构"""
    id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    timestamp: datetime = None
    filled_quantity: int = 0
    filled_price: Optional[float] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class Position:
    """持仓结构"""
    symbol: str
    quantity: int
    avg_cost: float
    current_price: float
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0

    @property
    def market_value(self) -> float:
        """市值"""
        return self.quantity * self.current_price

    @property
    def total_pnl(self) -> float:
        """总盈亏"""
        return self.unrealized_pnl + self.realized_pnl


@dataclass
class Signal:
    """交易信号"""
    symbol: str
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    strength: float  # 信号强度 0-1
    timestamp: datetime
    reason: str = ""
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class EventType(Enum):
    """事件类型"""
    MARKET_DATA = "market_data"
    SIGNAL = "signal"
    ORDER = "order"
    FILL = "fill"
    POSITION_UPDATE = "position_update"


@dataclass
class Event:
    """事件基类"""
    type: EventType
    timestamp: datetime
    data: Any

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class Component(ABC):
    """框架组件基类"""

    def __init__(self, name: str):
        self.name = name
        self.is_active = False

    @abstractmethod
    def start(self) -> None:
        """启动组件"""
        pass

    @abstractmethod
    def stop(self) -> None:
        """停止组件"""
        pass

    @abstractmethod
    def process_event(self, event: Event) -> None:
        """处理事件"""
        pass


class DataProvider(ABC):
    """数据提供者接口"""

    @abstractmethod
    def get_historical_data(self, symbol: str, start_date: datetime,
                          end_date: datetime) -> pd.DataFrame:
        """获取历史数据"""
        pass

    @abstractmethod
    def get_realtime_data(self, symbol: str) -> MarketData:
        """获取实时数据"""
        pass

    @abstractmethod
    def subscribe_data(self, symbol: str, callback) -> None:
        """订阅实时数据"""
        pass


class Broker(ABC):
    """券商接口"""

    @abstractmethod
    def submit_order(self, order: Order) -> str:
        """提交订单"""
        pass

    @abstractmethod
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        pass

    @abstractmethod
    def get_positions(self) -> List[Position]:
        """获取持仓"""
        pass

    @abstractmethod
    def get_account_info(self) -> Dict[str, float]:
        """获取账户信息"""
        pass


class Strategy(ABC):
    """策略基类"""

    def __init__(self, name: str):
        self.name = name
        self.is_active = False

    @abstractmethod
    def generate_signals(self, market_data: MarketData) -> List[Signal]:
        """生成交易信号"""
        pass

    @abstractmethod
    def on_market_data(self, data: MarketData) -> None:
        """处理市场数据"""
        pass

    @abstractmethod
    def on_signal(self, signal: Signal) -> None:
        """处理交易信号"""
        pass