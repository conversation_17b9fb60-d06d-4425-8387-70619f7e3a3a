"""
通用工具函数
"""
import logging
from typing import Optional, Union
from ib_insync import Ticker

logger = logging.getLogger(__name__)


def safe_format_price(value: Optional[float], prefix: str = "$") -> str:
    """安全格式化价格显示"""
    if value is None or value == 0:
        return "N/A"
    try:
        return f"{prefix}{value:.2f}"
    except (ValueError, TypeError):
        return "N/A"


def safe_format_number(value: Optional[Union[float, int]], decimal_places: int = 2) -> str:
    """安全格式化数字显示"""
    if value is None:
        return "N/A"
    try:
        return f"{value:.{decimal_places}f}"
    except (ValueError, TypeError):
        return "N/A"


def extract_ticker_data(ticker: Ticker) -> dict:
    """从Ticker对象中提取关键数据"""
    return {
        'last': ticker.last,
        'bid': ticker.bid,
        'ask': ticker.ask,
        'delta': ticker.modelGreeks.delta if ticker.modelGreeks else None,
        'gamma': ticker.modelGreeks.gamma if ticker.modelGreeks else None,
        'theta': ticker.modelGreeks.theta if ticker.modelGreeks else None,
        'vega': ticker.modelGreeks.vega if ticker.modelGreeks else None,
        'strike': ticker.contract.strike if hasattr(ticker.contract, 'strike') else None
    }


def calculate_rsi(prices, period: int = 14):
    """计算RSI指标"""
    import pandas as pd

    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

    # 避免除零错误
    rs = gain / loss.replace(0, float('inf'))
    rsi = 100 - (100 / (1 + rs))
    return rsi


def calculate_sma(prices, period: int = 20):
    """计算简单移动平均线"""
    return prices.rolling(window=period).mean()


def log_trade_action(action: str, symbol: str, details: dict):
    """记录交易动作"""
    logger.info(f"交易动作: {action} | 标的: {symbol} | 详情: {details}")


def format_position_summary(positions) -> str:
    """格式化持仓摘要"""
    if not positions:
        return "无持仓"

    summary = []
    for pos in positions:
        if pos.position != 0:
            cost_info = f"@ {safe_format_price(pos.avgCost)}" if pos.avgCost else ""
            summary.append(f"{pos.contract.symbol}: {pos.position} {cost_info}")

    return "\n".join(summary) if summary else "无有效持仓"