"""
实时数据流管理器 - 生产级实时数据处理
专为量化交易框架设计，提供稳定的实时数据流
"""
import logging
import threading
import time
from typing import Dict, List, Callable, Optional
from datetime import datetime, timezone
from collections import defaultdict, deque
from dataclasses import dataclass
import pytz

from .base import MarketData, Event, EventType
from .config import config

logger = logging.getLogger(__name__)


@dataclass
class DataSubscription:
    """数据订阅信息"""
    symbol: str
    callback: Callable
    last_update: datetime
    error_count: int = 0
    max_errors: int = 5


class MarketHoursManager:
    """市场时间管理器"""

    def __init__(self):
        self.ny_tz = pytz.timezone('America/New_York')
        self.market_calendars = {
            'US': {
                'open_time': (9, 30),   # 9:30 AM ET
                'close_time': (16, 0),  # 4:00 PM ET
                'weekend_days': [5, 6]  # Saturday, Sunday
            }
        }

    def is_market_open(self, market='US') -> bool:
        """检查市场是否开盘"""
        ny_time = datetime.now(self.ny_tz)
        calendar = self.market_calendars[market]

        # 检查是否为周末
        if ny_time.weekday() in calendar['weekend_days']:
            return False

        # 检查是否在交易时间内
        market_open = ny_time.replace(
            hour=calendar['open_time'][0],
            minute=calendar['open_time'][1],
            second=0,
            microsecond=0
        )
        market_close = ny_time.replace(
            hour=calendar['close_time'][0],
            minute=calendar['close_time'][1],
            second=0,
            microsecond=0
        )

        return market_open <= ny_time <= market_close

    def get_market_status(self, market='US') -> Dict:
        """获取市场状态信息"""
        ny_time = datetime.now(self.ny_tz)

        return {
            'current_time': ny_time,
            'is_open': self.is_market_open(market),
            'is_weekend': ny_time.weekday() in self.market_calendars[market]['weekend_days'],
            'timezone': 'America/New_York'
        }


class DataQualityMonitor:
    """数据质量监控器"""

    def __init__(self, window_size=100):
        self.window_size = window_size
        self.data_history = defaultdict(lambda: deque(maxlen=window_size))
        self.quality_metrics = defaultdict(dict)

    def add_data_point(self, symbol: str, data: MarketData):
        """添加数据点用于质量分析"""
        self.data_history[symbol].append({
            'timestamp': data.timestamp,
            'price': data.close,
            'volume': data.volume,
            'has_bid_ask': data.bid is not None and data.ask is not None
        })

        self._update_quality_metrics(symbol)

    def _update_quality_metrics(self, symbol: str):
        """更新数据质量指标"""
        history = list(self.data_history[symbol])
        if len(history) < 2:
            return

        # 计算数据新鲜度
        latest_time = history[-1]['timestamp']
        age_seconds = (datetime.now(timezone.utc) - latest_time).total_seconds()

        # 计算价格跳跃
        prices = [h['price'] for h in history if h['price'] > 0]
        price_jumps = 0
        if len(prices) > 1:
            for i in range(1, len(prices)):
                change_pct = abs(prices[i] - prices[i-1]) / prices[i-1]
                if change_pct > 0.05:  # 5%以上变化视为跳跃
                    price_jumps += 1

        # 计算Bid-Ask可用性
        bid_ask_coverage = sum(1 for h in history if h['has_bid_ask']) / len(history)

        self.quality_metrics[symbol] = {
            'data_age_seconds': age_seconds,
            'price_jump_rate': price_jumps / len(history) if history else 0,
            'bid_ask_coverage': bid_ask_coverage,
            'sample_count': len(history),
            'quality_score': self._calculate_quality_score(age_seconds, price_jumps / len(history), bid_ask_coverage)
        }

    def _calculate_quality_score(self, age_seconds: float, jump_rate: float, bid_ask_coverage: float) -> float:
        """计算数据质量评分 (0-100)"""
        # 新鲜度评分 (60秒内为满分)
        freshness_score = max(0, 100 - age_seconds)

        # 稳定性评分 (跳跃率越低越好)
        stability_score = max(0, 100 - jump_rate * 1000)

        # 完整性评分
        completeness_score = bid_ask_coverage * 100

        # 综合评分
        return (freshness_score * 0.4 + stability_score * 0.3 + completeness_score * 0.3)

    def get_quality_report(self, symbol: str) -> Dict:
        """获取数据质量报告"""
        return self.quality_metrics.get(symbol, {})


class RealtimeDataManager:
    """实时数据管理器 - 生产级实现"""

    def __init__(self, data_provider):
        self.data_provider = data_provider
        self.subscriptions: Dict[str, DataSubscription] = {}
        self.event_handlers: List[Callable] = []

        # 管理组件
        self.market_hours = MarketHoursManager()
        self.quality_monitor = DataQualityMonitor()

        # 运行状态
        self.is_running = False
        self.worker_thread = None
        self.update_interval = 1.0  # 秒

        # 性能监控
        self.stats = {
            'updates_processed': 0,
            'errors_encountered': 0,
            'start_time': None
        }

        self._lock = threading.Lock()

    def subscribe(self, symbol: str, callback: Callable) -> bool:
        """订阅实时数据"""
        with self._lock:
            try:
                subscription = DataSubscription(
                    symbol=symbol,
                    callback=callback,
                    last_update=datetime.now(timezone.utc)
                )

                self.subscriptions[symbol] = subscription
                logger.info(f"📡 订阅实时数据: {symbol}")
                return True

            except Exception as e:
                logger.error(f"❌ 订阅 {symbol} 失败: {e}")
                return False

    def unsubscribe(self, symbol: str) -> bool:
        """取消订阅"""
        with self._lock:
            if symbol in self.subscriptions:
                del self.subscriptions[symbol]
                logger.info(f"🚫 取消订阅: {symbol}")
                return True
            return False

    def add_event_handler(self, handler: Callable):
        """添加事件处理器"""
        self.event_handlers.append(handler)

    def start(self):
        """启动实时数据流"""
        if self.is_running:
            logger.warning("实时数据管理器已在运行")
            return

        self.is_running = True
        self.stats['start_time'] = datetime.now()

        self.worker_thread = threading.Thread(target=self._data_worker, daemon=True)
        self.worker_thread.start()

        logger.info("🚀 实时数据管理器已启动")

    def stop(self):
        """停止实时数据流"""
        self.is_running = False

        if self.worker_thread:
            self.worker_thread.join(timeout=5)

        logger.info("⏹️ 实时数据管理器已停止")

    def _data_worker(self):
        """数据工作线程"""
        logger.info("🔄 实时数据工作线程启动")

        while self.is_running:
            try:
                # 检查市场状态
                market_status = self.market_hours.get_market_status()

                if market_status['is_open']:
                    self._update_all_subscriptions()
                else:
                    # 市场关闭时降低更新频率
                    time.sleep(self.update_interval * 10)
                    continue

                time.sleep(self.update_interval)

            except Exception as e:
                logger.error(f"❌ 数据工作线程错误: {e}")
                self.stats['errors_encountered'] += 1
                time.sleep(self.update_interval * 2)  # 错误后等待更长时间

    def _update_all_subscriptions(self):
        """更新所有订阅"""
        with self._lock:
            for symbol, subscription in list(self.subscriptions.items()):
                try:
                    self._update_subscription(symbol, subscription)
                except Exception as e:
                    logger.error(f"❌ 更新 {symbol} 订阅失败: {e}")
                    subscription.error_count += 1

                    # 错误次数过多时取消订阅
                    if subscription.error_count >= subscription.max_errors:
                        logger.warning(f"⚠️ {symbol} 错误次数过多，自动取消订阅")
                        del self.subscriptions[symbol]

    def _update_subscription(self, symbol: str, subscription: DataSubscription):
        """更新单个订阅"""
        try:
            # 获取实时数据
            market_data = self.data_provider.get_realtime_data(symbol)

            if market_data:
                # 数据质量监控
                self.quality_monitor.add_data_point(symbol, market_data)

                # 更新订阅状态
                subscription.last_update = datetime.now(timezone.utc)
                subscription.error_count = 0

                # 调用回调函数
                subscription.callback(market_data)

                # 触发事件
                event = Event(
                    type=EventType.MARKET_DATA,
                    timestamp=market_data.timestamp,
                    data=market_data
                )
                self._trigger_event(event)

                self.stats['updates_processed'] += 1

        except Exception as e:
            subscription.error_count += 1
            raise e

    def _trigger_event(self, event: Event):
        """触发事件"""
        for handler in self.event_handlers:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"❌ 事件处理器错误: {e}")

    def get_statistics(self) -> Dict:
        """获取运行统计"""
        uptime = (datetime.now() - self.stats['start_time']).total_seconds() if self.stats['start_time'] else 0

        return {
            'is_running': self.is_running,
            'uptime_seconds': uptime,
            'active_subscriptions': len(self.subscriptions),
            'updates_processed': self.stats['updates_processed'],
            'errors_encountered': self.stats['errors_encountered'],
            'update_rate': self.stats['updates_processed'] / uptime if uptime > 0 else 0,
            'market_open': self.market_hours.is_market_open()
        }

    def get_subscription_status(self) -> Dict:
        """获取订阅状态"""
        status = {}

        with self._lock:
            for symbol, sub in self.subscriptions.items():
                quality = self.quality_monitor.get_quality_report(symbol)
                age = (datetime.now(timezone.utc) - sub.last_update).total_seconds()

                status[symbol] = {
                    'last_update_seconds_ago': age,
                    'error_count': sub.error_count,
                    'data_quality_score': quality.get('quality_score', 0),
                    'is_healthy': age < 10 and sub.error_count < 3
                }

        return status