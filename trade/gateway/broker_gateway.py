"""
企业级交易接口层 - 券商网关
提供统一的券商接口抽象，支持连接管理、协议封装、流量控制
"""
import logging
import time
import threading
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timezone
from dataclasses import dataclass
from collections import defaultdict, deque
from enum import Enum
import asyncio
from concurrent.futures import ThreadPoolExecutor

from ..core.base import Order, OrderStatus, MarketData, Position

logger = logging.getLogger(__name__)


@dataclass
class ConnectionConfig:
    """连接配置"""
    host: str = "127.0.0.1"
    port: int = 7497
    client_id: int = 1
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 2.0
    heartbeat_interval: float = 30.0


@dataclass
class RateLimitConfig:
    """流量控制配置"""
    max_requests_per_second: int = 50
    max_orders_per_second: int = 10
    max_market_data_requests: int = 100
    burst_allowance: int = 10


class ConnectionState(Enum):
    """连接状态"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"


class RateLimiter:
    """流量控制器"""
    
    def __init__(self, config: RateLimitConfig):
        self.config = config
        self.request_times = deque()
        self.order_times = deque()
        self.market_data_count = 0
        self._lock = threading.Lock()
    
    def can_make_request(self, request_type: str = "general") -> bool:
        """检查是否可以发送请求"""
        with self._lock:
            now = time.time()
            
            if request_type == "order":
                # 清理过期的订单请求时间
                while self.order_times and now - self.order_times[0] > 1.0:
                    self.order_times.popleft()
                
                return len(self.order_times) < self.config.max_orders_per_second
            
            elif request_type == "market_data":
                return self.market_data_count < self.config.max_market_data_requests
            
            else:
                # 清理过期的一般请求时间
                while self.request_times and now - self.request_times[0] > 1.0:
                    self.request_times.popleft()
                
                return len(self.request_times) < self.config.max_requests_per_second
    
    def record_request(self, request_type: str = "general"):
        """记录请求"""
        with self._lock:
            now = time.time()
            
            if request_type == "order":
                self.order_times.append(now)
            elif request_type == "market_data":
                self.market_data_count += 1
            else:
                self.request_times.append(now)
    
    def wait_if_needed(self, request_type: str = "general") -> float:
        """如果需要，等待直到可以发送请求"""
        wait_time = 0.0
        while not self.can_make_request(request_type):
            time.sleep(0.1)
            wait_time += 0.1
            if wait_time > 5.0:  # 最多等待5秒
                logger.warning(f"流量控制等待超时: {request_type}")
                break
        return wait_time


class ConnectionManager:
    """连接管理器"""
    
    def __init__(self, config: ConnectionConfig):
        self.config = config
        self.state = ConnectionState.DISCONNECTED
        self.connection = None
        self.last_heartbeat = None
        self.retry_count = 0
        self.error_message = None
        
        # 事件回调
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
        
        # 心跳线程
        self._heartbeat_thread = None
        self._stop_heartbeat = threading.Event()
    
    def connect(self) -> bool:
        """连接到券商"""
        self.state = ConnectionState.CONNECTING
        
        for attempt in range(self.config.max_retries):
            try:
                logger.info(f"🔌 连接尝试 {attempt + 1}/{self.config.max_retries}: "
                          f"{self.config.host}:{self.config.port}")
                
                # 实际连接逻辑由子类实现
                success = self._do_connect()
                
                if success:
                    self.state = ConnectionState.CONNECTED
                    self.retry_count = 0
                    self.error_message = None
                    self.last_heartbeat = datetime.now(timezone.utc)
                    
                    # 启动心跳
                    self._start_heartbeat()
                    
                    if self.on_connected:
                        self.on_connected()
                    
                    logger.info("✅ 连接成功")
                    return True
                
            except Exception as e:
                self.error_message = str(e)
                logger.warning(f"连接尝试 {attempt + 1} 失败: {e}")
                
                if attempt < self.config.max_retries - 1:
                    delay = self.config.retry_delay * (2 ** attempt)  # 指数退避
                    time.sleep(delay)
        
        self.state = ConnectionState.ERROR
        if self.on_error:
            self.on_error(self.error_message)
        
        logger.error(f"❌ 连接失败: {self.error_message}")
        return False
    
    def disconnect(self):
        """断开连接"""
        if self.state == ConnectionState.CONNECTED:
            self._stop_heartbeat.set()
            if self._heartbeat_thread:
                self._heartbeat_thread.join(timeout=1.0)
            
            self._do_disconnect()
            self.state = ConnectionState.DISCONNECTED
            
            if self.on_disconnected:
                self.on_disconnected()
            
            logger.info("🔌 连接已断开")
    
    def _start_heartbeat(self):
        """启动心跳线程"""
        self._stop_heartbeat.clear()
        self._heartbeat_thread = threading.Thread(target=self._heartbeat_worker)
        self._heartbeat_thread.daemon = True
        self._heartbeat_thread.start()
    
    def _heartbeat_worker(self):
        """心跳工作线程"""
        while not self._stop_heartbeat.wait(self.config.heartbeat_interval):
            try:
                if self.state == ConnectionState.CONNECTED:
                    # 检查连接健康状态
                    if self._check_connection_health():
                        self.last_heartbeat = datetime.now(timezone.utc)
                    else:
                        logger.warning("💔 心跳检查失败，尝试重连")
                        self._reconnect()
            except Exception as e:
                logger.error(f"心跳检查错误: {e}")
    
    def _reconnect(self):
        """重连"""
        self.state = ConnectionState.RECONNECTING
        self.disconnect()
        time.sleep(1.0)
        self.connect()
    
    @abstractmethod
    def _do_connect(self) -> bool:
        """实际连接实现"""
        pass
    
    @abstractmethod
    def _do_disconnect(self):
        """实际断开实现"""
        pass
    
    @abstractmethod
    def _check_connection_health(self) -> bool:
        """检查连接健康状态"""
        pass


class BrokerGateway(ABC):
    """券商网关抽象基类"""
    
    def __init__(self, connection_config: ConnectionConfig, rate_limit_config: RateLimitConfig):
        self.connection_manager = ConnectionManager(connection_config)
        self.rate_limiter = RateLimiter(rate_limit_config)
        
        # 统计信息
        self.stats = {
            'orders_submitted': 0,
            'orders_filled': 0,
            'orders_cancelled': 0,
            'orders_rejected': 0,
            'api_calls': 0,
            'errors': 0,
            'start_time': datetime.now(timezone.utc)
        }
        
        # 事件处理器
        self.event_handlers: Dict[str, List[Callable]] = defaultdict(list)
    
    def connect(self) -> bool:
        """连接到券商"""
        return self.connection_manager.connect()
    
    def disconnect(self):
        """断开连接"""
        self.connection_manager.disconnect()
    
    def add_event_handler(self, event_type: str, handler: Callable):
        """添加事件处理器"""
        self.event_handlers[event_type].append(handler)
    
    def _emit_event(self, event_type: str, data: Any):
        """触发事件"""
        for handler in self.event_handlers[event_type]:
            try:
                handler(data)
            except Exception as e:
                logger.error(f"事件处理器错误 {event_type}: {e}")
    
    @abstractmethod
    def submit_order(self, order: Order) -> bool:
        """提交订单"""
        pass
    
    @abstractmethod
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        pass
    
    @abstractmethod
    def get_order_status(self, order_id: str) -> Optional[Order]:
        """获取订单状态"""
        pass
    
    @abstractmethod
    def get_positions(self) -> List[Position]:
        """获取持仓"""
        pass
    
    @abstractmethod
    def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        pass
    
    @abstractmethod
    def subscribe_market_data(self, symbol: str, callback: Callable):
        """订阅市场数据"""
        pass
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        uptime = (datetime.now(timezone.utc) - self.stats['start_time']).total_seconds()
        
        return {
            **self.stats,
            'uptime_seconds': uptime,
            'connection_state': self.connection_manager.state.value,
            'last_heartbeat': self.connection_manager.last_heartbeat,
            'orders_per_minute': (self.stats['orders_submitted'] / uptime * 60) if uptime > 0 else 0,
            'error_rate': (self.stats['errors'] / self.stats['api_calls']) if self.stats['api_calls'] > 0 else 0
        }
