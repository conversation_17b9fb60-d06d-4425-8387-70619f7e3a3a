"""
Interactive Brokers 企业级网关实现
基于 ib_insync 的专业交易接口层
"""
import logging
import time
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timezone
import threading

from ib_insync import IB, Stock, Forex, Contract, MarketOrder, LimitOrder, StopOrder
from ib_insync import Trade, Fill, OrderStatus as IBOrderStatus
from ib_insync import util

from .broker_gateway import BrokerGateway, ConnectionManager, ConnectionConfig, RateLimitConfig, ConnectionState
from ..core.base import Order, OrderStatus, OrderType, OrderSide, TimeInForce, MarketData, Position

logger = logging.getLogger(__name__)


class IBConnectionManager(ConnectionManager):
    """IB连接管理器"""
    
    def __init__(self, config: ConnectionConfig):
        super().__init__(config)
        self.ib = IB()
        
        # 设置IB事件处理
        self.ib.connectedEvent += self._on_ib_connected
        self.ib.disconnectedEvent += self._on_ib_disconnected
        self.ib.errorEvent += self._on_ib_error
    
    def _do_connect(self) -> bool:
        """实际连接到IB"""
        try:
            self.ib.connect(
                host=self.config.host,
                port=self.config.port,
                clientId=self.config.client_id,
                timeout=self.config.timeout
            )
            return True
        except Exception as e:
            logger.error(f"IB连接失败: {e}")
            return False
    
    def _do_disconnect(self):
        """断开IB连接"""
        try:
            self.ib.disconnect()
        except Exception as e:
            logger.error(f"IB断开连接错误: {e}")
    
    def _check_connection_health(self) -> bool:
        """检查IB连接健康状态"""
        try:
            # 通过请求当前时间来检查连接
            return self.ib.isConnected()
        except Exception:
            return False
    
    def _on_ib_connected(self):
        """IB连接成功回调"""
        logger.info("📡 IB API连接已建立")
    
    def _on_ib_disconnected(self):
        """IB断开连接回调"""
        logger.warning("📡 IB API连接已断开")
    
    def _on_ib_error(self, reqId, errorCode, errorString, contract):
        """IB错误回调"""
        logger.error(f"IB错误 [{errorCode}]: {errorString} (reqId: {reqId})")


class IBGateway(BrokerGateway):
    """Interactive Brokers 企业级网关"""
    
    def __init__(self, connection_config: ConnectionConfig, rate_limit_config: RateLimitConfig):
        super().__init__(connection_config, rate_limit_config)
        
        # 使用专门的IB连接管理器
        self.connection_manager = IBConnectionManager(connection_config)
        self.ib = self.connection_manager.ib
        
        # 订单映射
        self.order_map: Dict[str, Trade] = {}
        self.reverse_order_map: Dict[int, str] = {}
        
        # 市场数据订阅
        self.market_data_subscriptions: Dict[str, Any] = {}
        
        # 设置IB事件处理
        self._setup_ib_events()
    
    def _setup_ib_events(self):
        """设置IB事件处理"""
        self.ib.orderStatusEvent += self._on_order_status
        self.ib.execDetailsEvent += self._on_execution
        self.ib.commissionReportEvent += self._on_commission
        self.ib.positionEvent += self._on_position_update
        self.ib.accountValueEvent += self._on_account_update
    
    def submit_order(self, order: Order) -> bool:
        """提交订单到IB"""
        if not self.connection_manager.state == ConnectionState.CONNECTED:
            logger.error("❌ 未连接到IB，无法提交订单")
            return False
        
        # 流量控制检查
        wait_time = self.rate_limiter.wait_if_needed("order")
        if wait_time > 0:
            logger.info(f"⏱️ 流量控制等待: {wait_time:.2f}秒")
        
        try:
            # 创建IB合约
            contract = self._create_contract(order.symbol)
            if not contract:
                logger.error(f"❌ 无法创建合约: {order.symbol}")
                return False
            
            # 创建IB订单
            ib_order = self._create_ib_order(order)
            if not ib_order:
                logger.error(f"❌ 无法创建IB订单: {order.order_id}")
                return False
            
            # 提交订单
            trade = self.ib.placeOrder(contract, ib_order)
            
            # 记录订单映射
            self.order_map[order.order_id] = trade
            self.reverse_order_map[trade.order.orderId] = order.order_id
            
            # 更新统计
            self.stats['orders_submitted'] += 1
            self.stats['api_calls'] += 1
            self.rate_limiter.record_request("order")
            
            # 更新订单状态
            order.update_status(OrderStatus.SUBMITTED)
            
            logger.info(f"📋 订单已提交: {order.symbol} {order.side.value} {order.quantity} @ {order.price or 'MKT'}")
            
            # 触发事件
            self._emit_event('order_submitted', order)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 提交订单失败: {e}")
            self.stats['errors'] += 1
            order.error_message = str(e)
            order.update_status(OrderStatus.REJECTED)
            return False
    
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        if order_id not in self.order_map:
            logger.error(f"❌ 订单不存在: {order_id}")
            return False
        
        try:
            trade = self.order_map[order_id]
            self.ib.cancelOrder(trade.order)
            
            self.stats['orders_cancelled'] += 1
            self.stats['api_calls'] += 1
            
            logger.info(f"🚫 订单取消请求已发送: {order_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 取消订单失败: {e}")
            self.stats['errors'] += 1
            return False
    
    def get_order_status(self, order_id: str) -> Optional[Order]:
        """获取订单状态"""
        if order_id not in self.order_map:
            return None
        
        try:
            trade = self.order_map[order_id]
            return self._convert_trade_to_order(trade)
        except Exception as e:
            logger.error(f"❌ 获取订单状态失败: {e}")
            return None
    
    def get_positions(self) -> List[Position]:
        """获取持仓"""
        try:
            positions = []
            for ib_position in self.ib.positions():
                position = Position(
                    symbol=ib_position.contract.symbol,
                    quantity=float(ib_position.position),
                    avg_cost=float(ib_position.avgCost) if ib_position.avgCost else 0.0,
                    market_price=0.0,  # 需要单独获取市场价格
                    market_value=float(ib_position.marketValue) if ib_position.marketValue else 0.0,
                    unrealized_pnl=float(ib_position.unrealizedPNL) if ib_position.unrealizedPNL else 0.0,
                    realized_pnl=float(ib_position.realizedPNL) if ib_position.realizedPNL else 0.0
                )
                positions.append(position)
            
            self.stats['api_calls'] += 1
            return positions
            
        except Exception as e:
            logger.error(f"❌ 获取持仓失败: {e}")
            self.stats['errors'] += 1
            return []
    
    def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            account_values = self.ib.accountValues()
            
            account_info = {}
            for value in account_values:
                if value.currency == 'BASE' or value.currency == 'USD':
                    account_info[value.tag] = {
                        'value': float(value.value) if value.value else 0.0,
                        'currency': value.currency,
                        'account': value.account
                    }
            
            self.stats['api_calls'] += 1
            return account_info
            
        except Exception as e:
            logger.error(f"❌ 获取账户信息失败: {e}")
            self.stats['errors'] += 1
            return {}
    
    def subscribe_market_data(self, symbol: str, callback: Callable):
        """订阅市场数据"""
        try:
            contract = self._create_contract(symbol)
            if not contract:
                logger.error(f"❌ 无法创建合约进行数据订阅: {symbol}")
                return False
            
            # 检查流量限制
            if not self.rate_limiter.can_make_request("market_data"):
                logger.warning(f"⚠️ 市场数据订阅达到限制: {symbol}")
                return False
            
            ticker = self.ib.reqMktData(contract)
            self.market_data_subscriptions[symbol] = {
                'ticker': ticker,
                'callback': callback,
                'contract': contract
            }
            
            # 设置数据更新回调
            ticker.updateEvent += lambda: self._on_market_data_update(symbol, ticker, callback)
            
            self.rate_limiter.record_request("market_data")
            self.stats['api_calls'] += 1
            
            logger.info(f"📊 已订阅市场数据: {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 订阅市场数据失败 {symbol}: {e}")
            self.stats['errors'] += 1
            return False
    
    def _create_contract(self, symbol: str) -> Optional[Contract]:
        """创建IB合约"""
        try:
            # 简单的合约创建逻辑，实际应用中需要更复杂的逻辑
            if '/' in symbol or len(symbol) == 6:  # 外汇对
                base, quote = symbol.split('/') if '/' in symbol else (symbol[:3], symbol[3:])
                return Forex(f"{base}{quote}")
            else:  # 股票
                return Stock(symbol, 'SMART', 'USD')
        except Exception as e:
            logger.error(f"创建合约失败 {symbol}: {e}")
            return None
    
    def _create_ib_order(self, order: Order):
        """创建IB订单对象"""
        try:
            action = order.side.value
            quantity = abs(order.quantity)
            
            if order.order_type == OrderType.MARKET:
                return MarketOrder(action, quantity)
            elif order.order_type == OrderType.LIMIT:
                return LimitOrder(action, quantity, order.price)
            elif order.order_type == OrderType.STOP:
                return StopOrder(action, quantity, order.stop_price)
            else:
                logger.error(f"不支持的订单类型: {order.order_type}")
                return None
                
        except Exception as e:
            logger.error(f"创建IB订单失败: {e}")
            return None
    
    def _convert_trade_to_order(self, trade: Trade) -> Order:
        """将IB Trade转换为内部Order对象"""
        # 这里需要实现详细的转换逻辑
        # 暂时返回基础实现
        order = Order()
        order.order_id = self.reverse_order_map.get(trade.order.orderId, "")
        order.symbol = trade.contract.symbol
        order.quantity = float(trade.order.totalQuantity)
        # ... 其他字段转换
        return order
    
    def _on_order_status(self, trade: Trade):
        """订单状态更新回调"""
        try:
            order_id = self.reverse_order_map.get(trade.order.orderId)
            if order_id:
                order = self._convert_trade_to_order(trade)
                self._emit_event('order_status_update', order)
        except Exception as e:
            logger.error(f"处理订单状态更新失败: {e}")
    
    def _on_execution(self, trade: Trade, fill: Fill):
        """订单执行回调"""
        try:
            order_id = self.reverse_order_map.get(trade.order.orderId)
            if order_id:
                self.stats['orders_filled'] += 1
                self._emit_event('order_filled', {'order_id': order_id, 'fill': fill})
        except Exception as e:
            logger.error(f"处理订单执行失败: {e}")
    
    def _on_commission(self, trade: Trade, fill: Fill, report):
        """佣金报告回调"""
        try:
            order_id = self.reverse_order_map.get(trade.order.orderId)
            if order_id:
                self._emit_event('commission_report', {'order_id': order_id, 'report': report})
        except Exception as e:
            logger.error(f"处理佣金报告失败: {e}")
    
    def _on_position_update(self, position):
        """持仓更新回调"""
        try:
            self._emit_event('position_update', position)
        except Exception as e:
            logger.error(f"处理持仓更新失败: {e}")
    
    def _on_account_update(self, value):
        """账户更新回调"""
        try:
            self._emit_event('account_update', value)
        except Exception as e:
            logger.error(f"处理账户更新失败: {e}")
    
    def _on_market_data_update(self, symbol: str, ticker, callback: Callable):
        """市场数据更新回调"""
        try:
            if ticker.last and not ticker.last != ticker.last:  # 检查不是NaN
                market_data = MarketData(
                    symbol=symbol,
                    timestamp=datetime.now(timezone.utc),
                    open=float(ticker.open) if ticker.open else 0.0,
                    high=float(ticker.high) if ticker.high else 0.0,
                    low=float(ticker.low) if ticker.low else 0.0,
                    close=float(ticker.last) if ticker.last else 0.0,
                    volume=int(ticker.volume) if ticker.volume else 0,
                    bid=float(ticker.bid) if ticker.bid else 0.0,
                    ask=float(ticker.ask) if ticker.ask else 0.0,
                    bid_size=int(ticker.bidSize) if ticker.bidSize else 0,
                    ask_size=int(ticker.askSize) if ticker.askSize else 0
                )
                callback(market_data)
        except Exception as e:
            logger.error(f"处理市场数据更新失败 {symbol}: {e}")
