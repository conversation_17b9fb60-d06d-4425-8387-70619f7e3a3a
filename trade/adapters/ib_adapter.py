"""
Interactive Brokers 适配器 - 将IB接口适配到量化框架
"""
import logging
from typing import Dict, List, Optional
from datetime import datetime
import pandas as pd

from ..core.base import DataProvider, Broker, MarketData, Order, Position, OrderStatus, OrderSide, OrderType
from ..core.config import config
from ib_insync import *
from ib_insync import MarketOrder, LimitOrder


logger = logging.getLogger(__name__)


class IBClient:
    """IB客户端封装类 - 生产级连接管理"""

    def __init__(self, client_id=None):
        self.ib = IB()
        self.connected = False
        self.client_id = client_id
        self.connection_timeout = 30
        self.retry_count = 3
        self.last_error = None

        # 设置IB事件处理器
        self.ib.errorEvent += self._on_error
        self.ib.disconnectedEvent += self._on_disconnected

    def connect(self) -> bool:
        """连接到IB Gateway - 带重试机制"""
        for attempt in range(self.retry_count):
            try:
                ib_config = config.get('ib')
                client_id = self.client_id if self.client_id is not None else ib_config['client_id']

                logger.info(f"连接尝试 {attempt + 1}/{self.retry_count}: {ib_config['host']}:{ib_config['port']} (Client ID: {client_id})")

                self.ib.connect(
                    host=ib_config['host'],
                    port=ib_config['port'],
                    clientId=client_id,
                    timeout=self.connection_timeout
                )

                # 验证连接
                if self.ib.isConnected():
                    self.connected = True
                    self.last_error = None
                    logger.info(f"✅ 成功连接到IB Gateway (Client ID: {client_id})")
                    return True

            except Exception as e:
                self.last_error = str(e)
                logger.warning(f"连接尝试 {attempt + 1} 失败: {e}")
                if attempt < self.retry_count - 1:
                    import time
                    time.sleep(2 ** attempt)  # 指数退避

        logger.error(f"❌ 连接IB Gateway失败 (已尝试 {self.retry_count} 次): {self.last_error}")
        return False

    def disconnect(self):
        """断开连接"""
        if self.connected and self.ib.isConnected():
            try:
                self.ib.disconnect()
                logger.info("✅ 已断开IB Gateway连接")
            except Exception as e:
                logger.warning(f"断开连接时出现警告: {e}")
            finally:
                self.connected = False

    def _on_error(self, reqId, errorCode, errorString, contract=None):
        """IB错误事件处理"""
        if errorCode in [502, 504]:  # 连接错误
            logger.error(f"🔴 IB连接错误 {errorCode}: {errorString}")
            self.connected = False
        elif errorCode == 10089:  # 数据权限错误
            logger.warning(f"📊 数据权限问题 {errorCode}: {errorString}")
        elif errorCode >= 2100:  # 一般性警告
            logger.warning(f"⚠️ IB警告 {errorCode}: {errorString}")
        else:
            logger.error(f"❌ IB错误 {errorCode}: {errorString}")

    def _on_disconnected(self):
        """断连事件处理"""
        logger.warning("🔴 IB连接意外断开")
        self.connected = False

    def ensure_connected(self) -> bool:
        """确保连接可用"""
        if not self.connected or not self.ib.isConnected():
            logger.info("🔄 检测到连接断开，尝试重连...")
            return self.connect()
        return True

    def get_stock_contract(self, symbol: str = None) -> Optional[Stock]:
        """获取股票合约"""
        strategy_config = config.get('strategy')
        symbol = symbol or strategy_config['symbol']
        try:
            contract = Stock(
                symbol=symbol,
                exchange=strategy_config['exchange'],
                currency=strategy_config['currency']
            )
            self.ib.qualifyContracts(contract)
            logger.info(f"成功获取{symbol}合约信息")
            return contract
        except Exception as e:
            logger.error(f"获取{symbol}合约失败: {e}")
            return None

    def get_historical_data(self, contract, duration: str = '1 Y', bar_size: str = '1 day') -> Optional[pd.DataFrame]:
        """获取历史数据"""
        try:
            bars = self.ib.reqHistoricalData(
                contract=contract,
                endDateTime='',
                durationStr=duration,
                barSizeSetting=bar_size,
                whatToShow='TRADES',
                useRTH=True,
                formatDate=1
            )

            if not bars:
                logger.error("无法获取历史数据")
                return None

            df = util.df(bars)
            df.set_index('date', inplace=True)
            logger.info(f"成功获取{len(df)}条历史数据")
            return df

        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return None

    def get_market_data(self, contract, timeout=5):
        """获取实时市场数据 - 生产级实现"""
        if not self.ensure_connected():
            raise ConnectionError("无法连接到IB Gateway")

        try:
            # 取消之前的订阅（如果存在）
            self.ib.cancelMktData(contract)

            # 请求市场数据
            ticker = self.ib.reqMktData(contract, '', False, False)

            # 等待数据，使用更智能的等待机制
            for _ in range(timeout * 2):  # 最多等待timeout秒
                self.ib.sleep(0.5)
                if ticker.last or ticker.bid or ticker.ask or ticker.close:
                    break
            else:
                logger.warning(f"⏰ 获取 {contract.symbol} 实时数据超时")

            return ticker

        except Exception as e:
            logger.error(f"❌ 获取 {contract.symbol} 实时数据失败: {e}")
            return None

    def get_account_info(self) -> Dict[str, float]:
        """获取账户信息 - 增强版本"""
        if not self.ensure_connected():
            raise ConnectionError("无法连接到IB Gateway")

        try:
            account_values = self.ib.accountValues()
            account_info = {}

            # 关键账户指标
            key_fields = {
                'NetLiquidation': '净资产',
                'TotalCashValue': '现金总额',
                'GrossPositionValue': '持仓总值',
                'AvailableFunds': '可用资金',
                'BuyingPower': '购买力',
                'DayTradesRemaining': '日内交易剩余次数'
            }

            for value in account_values:
                if value.tag in key_fields:
                    try:
                        account_info[value.tag] = float(value.value)
                    except (ValueError, TypeError):
                        account_info[value.tag] = 0.0

            # 记录关键信息
            net_liq = account_info.get('NetLiquidation', 0)
            available = account_info.get('AvailableFunds', 0)
            logger.info(f"💰 账户状态: 净资产=${net_liq:,.2f}, 可用资金=${available:,.2f}")

            return account_info

        except Exception as e:
            logger.error(f"❌ 获取账户信息失败: {e}")
            return {}

    def get_current_positions(self):
        """获取当前持仓 - 增强版本"""
        if not self.ensure_connected():
            raise ConnectionError("无法连接到IB Gateway")

        try:
            positions = self.ib.positions()

            # 过滤有效持仓
            active_positions = [pos for pos in positions if pos.position != 0]

            if active_positions:
                logger.info(f"📊 当前持仓数量: {len(active_positions)}")
                for pos in active_positions:
                    logger.info(f"   {pos.contract.symbol}: {pos.position} @ ${pos.avgCost:.2f}")
            else:
                logger.info("📊 当前无持仓")

            return active_positions

        except Exception as e:
            logger.error(f"❌ 获取持仓失败: {e}")
            return []

    def create_market_order(self, action: str, quantity: int):
        """创建市价单"""
        return MarketOrder(action, quantity)

    def create_limit_order(self, action: str, quantity: int, limit_price: float):
        """创建限价单"""
        return LimitOrder(action, quantity, limit_price)

    def place_order(self, contract, order):
        """下单 - 生产级实现"""
        if not self.ensure_connected():
            raise ConnectionError("无法连接到IB Gateway")

        try:
            # 预检查合约
            qualified_contracts = self.ib.qualifyContracts(contract)
            if not qualified_contracts:
                raise ValueError(f"无效合约: {contract}")

            # 提交订单
            trade = self.ib.placeOrder(qualified_contracts[0], order)

            if trade:
                logger.info(f"📋 订单已提交: {trade.order.orderId} - {contract.symbol} {order.action} {order.totalQuantity}")
            else:
                raise RuntimeError("订单提交失败")

            return trade

        except Exception as e:
            logger.error(f"❌ 下单失败: {e}")
            raise

    def get_option_chain(self, stock_contract, expiration_days=30):
        """获取期权链数据"""
        try:
            from datetime import datetime, timedelta
            import math

            # 获取股票当前价格
            ticker = self.ib.reqMktData(stock_contract)
            self.ib.sleep(1)
            current_price = ticker.last or ticker.close

            if not current_price:
                logger.error("无法获取股票当前价格")
                return None

            # 获取期权链
            chains = self.ib.reqSecDefOptParams(
                underlyingSymbol=stock_contract.symbol,
                futFopExchange='',
                underlyingSecType=stock_contract.secType,
                underlyingConId=stock_contract.conId
            )

            if not chains:
                logger.error("无法获取期权链")
                return None

            # 选择最近的到期日
            target_date = datetime.now() + timedelta(days=expiration_days)
            best_chain = None
            min_diff = float('inf')

            for chain in chains:
                for exp_date in chain.expirations:
                    exp_datetime = datetime.strptime(exp_date, '%Y%m%d')
                    diff = abs((exp_datetime - target_date).days)
                    if diff < min_diff:
                        min_diff = diff
                        best_chain = chain
                        best_expiration = exp_date

            if not best_chain:
                logger.error("无法找到合适的到期日")
                return None

            # 选择合适的行权价（当前价格附近的几个价格）
            strikes = sorted([float(strike) for strike in best_chain.strikes])

            # 找到接近当前价格的行权价
            nearby_strikes = []
            for strike in strikes:
                if abs(strike - current_price) / current_price <= 0.15:  # 15%范围内
                    nearby_strikes.append(strike)

            # 获取具体期权合约数据
            option_data = {
                'stock_price': current_price,
                'expiration': best_expiration,
                'puts': [],
                'calls': []
            }

            for strike in nearby_strikes[:10]:  # 限制数量
                # Put期权
                put_contract = Option(
                    symbol=stock_contract.symbol,
                    lastTradeDateOrContractMonth=best_expiration,
                    strike=strike,
                    right='P',
                    exchange='SMART'
                )

                # Call期权
                call_contract = Option(
                    symbol=stock_contract.symbol,
                    lastTradeDateOrContractMonth=best_expiration,
                    strike=strike,
                    right='C',
                    exchange='SMART'
                )

                # 获取期权报价
                try:
                    self.ib.qualifyContracts(put_contract, call_contract)

                    put_ticker = self.ib.reqMktData(put_contract)
                    call_ticker = self.ib.reqMktData(call_contract)
                    self.ib.sleep(0.5)  # 等待数据

                    # Put数据
                    put_data = {
                        'strike': strike,
                        'bid': put_ticker.bid,
                        'ask': put_ticker.ask,
                        'last': put_ticker.last,
                        'volume': put_ticker.volume,
                        'openInterest': getattr(put_ticker, 'openInterest', 0),
                        'impliedVol': getattr(put_ticker, 'impliedVolatility', 0),
                        'delta': getattr(put_ticker, 'modelGreeks', {}).get('delta', 0) if hasattr(put_ticker, 'modelGreeks') else 0,
                        'moneyness': (current_price - strike) / current_price,  # 内在价值比例
                        'contract': put_contract
                    }

                    # Call数据
                    call_data = {
                        'strike': strike,
                        'bid': call_ticker.bid,
                        'ask': call_ticker.ask,
                        'last': call_ticker.last,
                        'volume': call_ticker.volume,
                        'openInterest': getattr(call_ticker, 'openInterest', 0),
                        'impliedVol': getattr(call_ticker, 'impliedVolatility', 0),
                        'delta': getattr(call_ticker, 'modelGreeks', {}).get('delta', 0) if hasattr(call_ticker, 'modelGreeks') else 0,
                        'moneyness': (strike - current_price) / current_price,  # 内在价值比例
                        'contract': call_contract
                    }

                    option_data['puts'].append(put_data)
                    option_data['calls'].append(call_data)

                except Exception as e:
                    logger.warning(f"获取行权价 {strike} 期权数据失败: {e}")
                    continue

            return option_data

        except Exception as e:
            logger.error(f"获取期权链失败: {e}")
            return None

    def get_target_delta_options(self, stock_contract, target_delta_put=-0.3, target_delta_call=0.3, expiration_days=30):
        """获取目标Delta的期权合约"""
        try:
            option_data = self.get_option_chain(stock_contract, expiration_days)
            if not option_data:
                return None

            result = {
                'stock_price': option_data['stock_price'],
                'expiration': option_data['expiration'],
                'target_put': None,
                'target_call': None
            }

            # 找到最接近目标Delta的Put期权
            best_put_diff = float('inf')
            for put in option_data['puts']:
                if put['delta'] and put['bid'] and put['ask']:
                    delta_diff = abs(put['delta'] - target_delta_put)
                    if delta_diff < best_put_diff:
                        best_put_diff = delta_diff
                        result['target_put'] = put

            # 找到最接近目标Delta的Call期权
            best_call_diff = float('inf')
            for call in option_data['calls']:
                if call['delta'] and call['bid'] and call['ask']:
                    delta_diff = abs(call['delta'] - target_delta_call)
                    if delta_diff < best_call_diff:
                        best_call_diff = delta_diff
                        result['target_call'] = call

            return result

        except Exception as e:
            logger.error(f"获取目标Delta期权失败: {e}")
            return None


class IBDataProvider(DataProvider):
    """IB数据提供者适配器"""

    def __init__(self, client_id=None):
        self.ib_client = IBClient(client_id)
        self.connected = False

    def connect(self) -> bool:
        """连接到IB"""
        if not self.connected:
            self.connected = self.ib_client.connect()
        return self.connected

    def disconnect(self):
        """断开连接"""
        if self.connected:
            self.ib_client.disconnect()
            self.connected = False

    def get_historical_data(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """获取历史数据"""
        if not self.connected:
            if not self.connect():
                raise ConnectionError("无法连接到IB Gateway")

        try:
            # 获取股票合约
            contract = self.ib_client.get_stock_contract(symbol)
            if not contract:
                raise ValueError(f"无法获取合约: {symbol}")

            # 计算持续时间
            duration_days = (end_date - start_date).days
            if duration_days <= 30:
                duration_str = f"{duration_days} D"
                bar_size = "1 hour"
            elif duration_days <= 365:
                duration_str = f"{duration_days} D"
                bar_size = "1 day"
            else:
                duration_str = "1 Y"
                bar_size = "1 day"

            # 获取历史数据
            df = self.ib_client.get_historical_data(contract, duration_str, bar_size)
            if df is not None and not df.empty:
                # 计算技术指标
                from ..data.data_manager import MarketDataProcessor
                processor = MarketDataProcessor()
                indicators = {
                    'rsi': {'period': 14},
                    'sma': {'period': 20}
                }
                df = processor.process_data(df, indicators)
                return df

            return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return pd.DataFrame()

    def get_realtime_data(self, symbol: str) -> MarketData:
        """获取实时数据"""
        if not self.connected:
            if not self.connect():
                raise ConnectionError("无法连接到IB Gateway")

        try:
            contract = self.ib_client.get_stock_contract(symbol)
            if not contract:
                raise ValueError(f"无法获取合约: {symbol}")

            ticker = self.ib_client.get_market_data(contract)
            if ticker:
                return MarketData(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    open=ticker.last or 0,  # IB实时数据可能没有OHLC
                    high=ticker.last or 0,
                    low=ticker.last or 0,
                    close=ticker.last or 0,
                    volume=0,
                    bid=ticker.bid,
                    ask=ticker.ask
                )

            raise ValueError("无法获取实时数据")

        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            raise

    def subscribe_data(self, symbol: str, callback):
        """订阅实时数据"""
        # IB的实时数据订阅实现较复杂，这里暂时用轮询方式
        # 在生产环境中应该使用IB的实时数据流
        pass


class IBBroker(Broker):
    """IB经纪商适配器"""

    def __init__(self, client_id=None):
        self.ib_client = IBClient(client_id)
        self.connected = False

    def connect(self) -> bool:
        """连接到IB"""
        if not self.connected:
            self.connected = self.ib_client.connect()
        return self.connected

    def disconnect(self):
        """断开连接"""
        if self.connected:
            self.ib_client.disconnect()
            self.connected = False

    def submit_order(self, order: Order) -> str:
        """提交订单"""
        if not self.connected:
            if not self.connect():
                raise ConnectionError("无法连接到IB Gateway")

        try:
            # 获取合约
            contract = self.ib_client.get_stock_contract(order.symbol)
            if not contract:
                raise ValueError(f"无法获取合约: {order.symbol}")

            # 转换订单类型
            if order.order_type == OrderType.MARKET:
                ib_order = self.ib_client.create_market_order(
                    action=order.side.value.upper(),
                    quantity=order.quantity
                )
            elif order.order_type == OrderType.LIMIT:
                ib_order = self.ib_client.create_limit_order(
                    action=order.side.value.upper(),
                    quantity=order.quantity,
                    limit_price=order.price
                )
            else:
                raise ValueError(f"不支持的订单类型: {order.order_type}")

            # 提交订单
            trade = self.ib_client.place_order(contract, ib_order)
            if trade:
                return str(trade.order.orderId)
            else:
                raise RuntimeError("订单提交失败")

        except Exception as e:
            logger.error(f"提交订单失败: {e}")
            raise

    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        try:
            # IB的取消订单实现
            # 这里需要实现具体的取消逻辑
            return True
        except Exception as e:
            logger.error(f"取消订单失败: {e}")
            return False

    def get_positions(self) -> List[Position]:
        """获取持仓"""
        if not self.connected:
            if not self.connect():
                return []

        try:
            ib_positions = self.ib_client.get_current_positions()
            positions = []

            for ib_pos in ib_positions:
                if ib_pos.position != 0:
                    # 获取当前价格 (简化处理)
                    current_price = ib_pos.avgCost  # 暂时使用平均成本

                    position = Position(
                        symbol=ib_pos.contract.symbol,
                        quantity=int(ib_pos.position),
                        avg_cost=ib_pos.avgCost,
                        current_price=current_price
                    )
                    positions.append(position)

            return positions

        except Exception as e:
            logger.error(f"获取持仓失败: {e}")
            return []

    def get_account_info(self) -> Dict[str, float]:
        """获取账户信息"""
        if not self.connected:
            if not self.connect():
                return {}

        try:
            return self.ib_client.get_account_info()
        except Exception as e:
            logger.error(f"获取账户信息失败: {e}")
            return {}


class IBFrameworkAdapter:
    """IB框架适配器主类"""

    def __init__(self):
        # Use different client IDs for data provider and broker to avoid conflicts
        base_client_id = config.get('ib')['client_id']
        self.data_provider = IBDataProvider(client_id=base_client_id)
        self.broker = IBBroker(client_id=base_client_id + 1)

    def connect(self) -> bool:
        """连接到IB"""
        data_connected = self.data_provider.connect()
        broker_connected = self.broker.connect()
        return data_connected and broker_connected

    def disconnect(self):
        """断开连接"""
        self.data_provider.disconnect()
        self.broker.disconnect()

    def __enter__(self):
        if self.connect():
            return self
        else:
            raise ConnectionError("无法连接到IB Gateway")

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()