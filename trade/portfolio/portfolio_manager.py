"""
企业级持仓资金管理模块
提供精确的持仓跟踪、资金同步、盈亏计算
"""
import logging
import threading
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, field
from collections import defaultdict
import json

from ..core.base import Position, Order, OrderSide
from ..oms.order_manager import OrderExecution

logger = logging.getLogger(__name__)


@dataclass
class AccountInfo:
    """账户信息"""
    account_id: str = ""
    currency: str = "USD"
    
    # 资金信息
    total_cash: float = 0.0
    available_cash: float = 0.0
    buying_power: float = 0.0
    
    # 保证金信息
    initial_margin: float = 0.0
    maintenance_margin: float = 0.0
    excess_liquidity: float = 0.0
    
    # 净值信息
    net_liquidation_value: float = 0.0
    gross_position_value: float = 0.0
    
    # 盈亏信息
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    
    # 更新时间
    last_update_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    @property
    def total_pnl(self) -> float:
        """总盈亏"""
        return self.realized_pnl + self.unrealized_pnl
    
    @property
    def margin_utilization(self) -> float:
        """保证金使用率"""
        if self.buying_power == 0:
            return 0.0
        return (self.buying_power - self.available_cash) / self.buying_power


@dataclass
class PortfolioSummary:
    """投资组合摘要"""
    total_positions: int = 0
    long_positions: int = 0
    short_positions: int = 0
    
    total_market_value: float = 0.0
    total_cost: float = 0.0
    total_pnl: float = 0.0
    total_realized_pnl: float = 0.0
    total_unrealized_pnl: float = 0.0
    
    largest_position_value: float = 0.0
    largest_position_symbol: str = ""
    
    concentration_risk: float = 0.0  # 最大持仓占比
    
    @property
    def total_return_percentage(self) -> float:
        """总收益率"""
        if self.total_cost == 0:
            return 0.0
        return (self.total_pnl / abs(self.total_cost)) * 100.0


class PositionTracker:
    """持仓跟踪器"""
    
    def __init__(self):
        self.positions: Dict[str, Position] = {}
        self.position_history: Dict[str, List[Position]] = defaultdict(list)
        self._lock = threading.RLock()
    
    def update_position_from_trade(self, symbol: str, quantity: float, price: float, 
                                 side: OrderSide, commission: float = 0.0):
        """从交易更新持仓"""
        with self._lock:
            if symbol not in self.positions:
                self.positions[symbol] = Position(symbol=symbol)
            
            position = self.positions[symbol]
            
            # 根据交易方向调整数量
            trade_quantity = quantity if side == OrderSide.BUY else -quantity
            
            # 更新持仓
            position.add_trade(trade_quantity, price, commission)
            
            # 记录历史
            self._record_position_history(symbol, position)
            
            logger.info(f"📊 持仓更新: {symbol} {position.quantity}@{position.avg_cost:.4f}")
    
    def update_market_prices(self, price_data: Dict[str, float]):
        """批量更新市场价格"""
        with self._lock:
            for symbol, price in price_data.items():
                if symbol in self.positions:
                    self.positions[symbol].update_market_price(price)
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """获取持仓"""
        return self.positions.get(symbol)
    
    def get_all_positions(self) -> List[Position]:
        """获取所有持仓"""
        with self._lock:
            return list(self.positions.values())
    
    def get_active_positions(self) -> List[Position]:
        """获取活跃持仓（非零持仓）"""
        with self._lock:
            return [pos for pos in self.positions.values() if not pos.is_flat]
    
    def _record_position_history(self, symbol: str, position: Position):
        """记录持仓历史"""
        # 创建持仓快照
        snapshot = Position(
            symbol=position.symbol,
            quantity=position.quantity,
            avg_cost=position.avg_cost,
            market_price=position.market_price,
            total_cost=position.total_cost,
            realized_pnl=position.realized_pnl,
            unrealized_pnl=position.unrealized_pnl,
            market_value=position.market_value,
            last_update_time=position.last_update_time,
            total_bought=position.total_bought,
            total_sold=position.total_sold,
            total_commission=position.total_commission
        )
        
        self.position_history[symbol].append(snapshot)
        
        # 保持历史记录大小
        if len(self.position_history[symbol]) > 1000:
            self.position_history[symbol] = self.position_history[symbol][-500:]


class FundManager:
    """资金管理器"""
    
    def __init__(self):
        self.account_info = AccountInfo()
        self.cash_flows: List[Dict[str, Any]] = []
        self._lock = threading.RLock()
    
    def update_account_info(self, account_data: Dict[str, Any]):
        """更新账户信息"""
        with self._lock:
            # 更新账户信息字段
            for key, value in account_data.items():
                if hasattr(self.account_info, key):
                    setattr(self.account_info, key, float(value) if isinstance(value, (int, float, str)) else value)
            
            self.account_info.last_update_time = datetime.now(timezone.utc)
            
            logger.debug(f"💰 账户信息更新: 净值={self.account_info.net_liquidation_value:.2f}")
    
    def record_cash_flow(self, amount: float, flow_type: str, description: str = ""):
        """记录资金流动"""
        with self._lock:
            cash_flow = {
                'timestamp': datetime.now(timezone.utc),
                'amount': amount,
                'type': flow_type,  # 'deposit', 'withdrawal', 'dividend', 'commission', etc.
                'description': description,
                'balance_after': self.account_info.total_cash
            }
            
            self.cash_flows.append(cash_flow)
            
            # 保持记录大小
            if len(self.cash_flows) > 10000:
                self.cash_flows = self.cash_flows[-5000:]
            
            logger.info(f"💸 资金流动: {flow_type} {amount:.2f} - {description}")
    
    def get_cash_flow_summary(self, days: int = 30) -> Dict[str, Any]:
        """获取资金流动摘要"""
        with self._lock:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            recent_flows = [cf for cf in self.cash_flows if cf['timestamp'] > cutoff_date]
            
            summary = {
                'total_inflows': sum(cf['amount'] for cf in recent_flows if cf['amount'] > 0),
                'total_outflows': sum(cf['amount'] for cf in recent_flows if cf['amount'] < 0),
                'net_flow': sum(cf['amount'] for cf in recent_flows),
                'transaction_count': len(recent_flows),
                'period_days': days
            }
            
            return summary


class PortfolioManager:
    """企业级投资组合管理器"""
    
    def __init__(self):
        self.position_tracker = PositionTracker()
        self.fund_manager = FundManager()
        
        # 事件处理
        self.event_handlers: Dict[str, List[Callable]] = defaultdict(list)
        
        # 统计信息
        self.stats = {
            'total_trades': 0,
            'total_commission': 0.0,
            'start_time': datetime.now(timezone.utc)
        }
        
        logger.info("📊 投资组合管理器初始化完成")
    
    def process_execution(self, execution: OrderExecution, order: Order):
        """处理订单执行"""
        # 更新持仓
        self.position_tracker.update_position_from_trade(
            symbol=order.symbol,
            quantity=execution.fill_quantity,
            price=execution.fill_price,
            side=order.side,
            commission=execution.commission
        )
        
        # 记录资金流动
        if execution.commission > 0:
            self.fund_manager.record_cash_flow(
                amount=-execution.commission,
                flow_type='commission',
                description=f"交易佣金: {order.symbol}"
            )
        
        # 更新统计
        self.stats['total_trades'] += 1
        self.stats['total_commission'] += execution.commission
        
        # 触发事件
        self._emit_event('position_updated', {
            'symbol': order.symbol,
            'execution': execution,
            'position': self.position_tracker.get_position(order.symbol)
        })
        
        logger.info(f"🔄 处理执行: {order.symbol} {execution.fill_quantity}@{execution.fill_price}")
    
    def update_market_data(self, market_data: Dict[str, float]):
        """更新市场数据"""
        self.position_tracker.update_market_prices(market_data)
        
        # 触发事件
        self._emit_event('market_data_updated', market_data)
    
    def update_account_info(self, account_data: Dict[str, Any]):
        """更新账户信息"""
        self.fund_manager.update_account_info(account_data)
        
        # 触发事件
        self._emit_event('account_updated', self.fund_manager.account_info)
    
    def get_portfolio_summary(self) -> PortfolioSummary:
        """获取投资组合摘要"""
        positions = self.position_tracker.get_active_positions()
        
        summary = PortfolioSummary()
        summary.total_positions = len(positions)
        
        for position in positions:
            if position.is_long:
                summary.long_positions += 1
            elif position.is_short:
                summary.short_positions += 1
            
            summary.total_market_value += position.market_value
            summary.total_cost += position.total_cost
            summary.total_realized_pnl += position.realized_pnl
            summary.total_unrealized_pnl += position.unrealized_pnl
            
            # 找出最大持仓
            position_value = abs(position.market_value)
            if position_value > summary.largest_position_value:
                summary.largest_position_value = position_value
                summary.largest_position_symbol = position.symbol
        
        summary.total_pnl = summary.total_realized_pnl + summary.total_unrealized_pnl
        
        # 计算集中度风险
        if summary.total_market_value > 0:
            summary.concentration_risk = (summary.largest_position_value / abs(summary.total_market_value)) * 100.0
        
        return summary
    
    def get_position_report(self) -> Dict[str, Any]:
        """获取持仓报告"""
        positions = self.position_tracker.get_active_positions()
        summary = self.get_portfolio_summary()
        
        position_details = []
        for position in positions:
            position_details.append({
                'symbol': position.symbol,
                'quantity': position.quantity,
                'avg_cost': position.avg_cost,
                'market_price': position.market_price,
                'market_value': position.market_value,
                'unrealized_pnl': position.unrealized_pnl,
                'realized_pnl': position.realized_pnl,
                'total_pnl': position.total_pnl,
                'pnl_percentage': position.pnl_percentage,
                'weight': (abs(position.market_value) / abs(summary.total_market_value) * 100.0) if summary.total_market_value != 0 else 0.0
            })
        
        # 按市值排序
        position_details.sort(key=lambda x: abs(x['market_value']), reverse=True)
        
        return {
            'summary': {
                'total_positions': summary.total_positions,
                'long_positions': summary.long_positions,
                'short_positions': summary.short_positions,
                'total_market_value': summary.total_market_value,
                'total_pnl': summary.total_pnl,
                'total_return_percentage': summary.total_return_percentage,
                'concentration_risk': summary.concentration_risk,
                'largest_position': summary.largest_position_symbol
            },
            'positions': position_details,
            'account_info': {
                'net_liquidation_value': self.fund_manager.account_info.net_liquidation_value,
                'total_cash': self.fund_manager.account_info.total_cash,
                'buying_power': self.fund_manager.account_info.buying_power,
                'margin_utilization': self.fund_manager.account_info.margin_utilization
            },
            'statistics': {
                **self.stats,
                'uptime_hours': (datetime.now(timezone.utc) - self.stats['start_time']).total_seconds() / 3600
            }
        }
    
    def get_risk_metrics(self) -> Dict[str, Any]:
        """获取风险指标"""
        positions = self.position_tracker.get_active_positions()
        summary = self.get_portfolio_summary()
        
        # 计算各种风险指标
        position_count = len(positions)
        long_exposure = sum(pos.market_value for pos in positions if pos.is_long)
        short_exposure = sum(abs(pos.market_value) for pos in positions if pos.is_short)
        gross_exposure = long_exposure + short_exposure
        net_exposure = long_exposure - short_exposure
        
        # 计算持仓分散度
        if position_count > 0:
            avg_position_size = gross_exposure / position_count
            position_sizes = [abs(pos.market_value) for pos in positions]
            position_variance = sum((size - avg_position_size) ** 2 for size in position_sizes) / position_count
            position_std = position_variance ** 0.5
            diversification_ratio = position_std / avg_position_size if avg_position_size > 0 else 0.0
        else:
            diversification_ratio = 0.0
        
        return {
            'position_count': position_count,
            'long_exposure': long_exposure,
            'short_exposure': short_exposure,
            'gross_exposure': gross_exposure,
            'net_exposure': net_exposure,
            'concentration_risk': summary.concentration_risk,
            'diversification_ratio': diversification_ratio,
            'leverage_ratio': gross_exposure / self.fund_manager.account_info.net_liquidation_value if self.fund_manager.account_info.net_liquidation_value > 0 else 0.0
        }
    
    def add_event_handler(self, event_type: str, handler: Callable):
        """添加事件处理器"""
        self.event_handlers[event_type].append(handler)
    
    def _emit_event(self, event_type: str, data: Any):
        """触发事件"""
        for handler in self.event_handlers[event_type]:
            try:
                handler(data)
            except Exception as e:
                logger.error(f"事件处理器错误 {event_type}: {e}")
