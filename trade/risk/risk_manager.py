"""
企业级风险管理模块
实现交易前后风控、规则引擎、多维度风险监控
"""
import logging
import threading
from typing import Dict, List, Optional, Callable, Any, Union
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from enum import Enum
import json
import uuid

from ..core.base import Order, OrderStatus, OrderSide, Position
from ..oms.order_manager import OrderExecution

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskCheckResult(Enum):
    """风险检查结果"""
    PASS = "pass"
    WARNING = "warning"
    REJECT = "reject"


@dataclass
class RiskLimit:
    """风险限制"""
    name: str
    limit_type: str  # 'position', 'daily_loss', 'order_size', etc.
    symbol: Optional[str] = None  # None表示全局限制
    max_value: float = 0.0
    current_value: float = 0.0
    currency: str = "USD"
    enabled: bool = True
    
    @property
    def utilization_rate(self) -> float:
        """使用率"""
        if self.max_value == 0:
            return 0.0
        return abs(self.current_value) / self.max_value
    
    @property
    def remaining_capacity(self) -> float:
        """剩余容量"""
        return self.max_value - abs(self.current_value)
    
    def is_breached(self) -> bool:
        """是否违反限制"""
        return self.enabled and abs(self.current_value) > self.max_value


@dataclass
class RiskEvent:
    """风险事件"""
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = ""
    risk_level: RiskLevel = RiskLevel.LOW
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    order_id: Optional[str] = None
    symbol: Optional[str] = None
    resolved: bool = False


class RiskRule(ABC):
    """风险规则抽象基类"""
    
    def __init__(self, name: str, enabled: bool = True):
        self.name = name
        self.enabled = enabled
    
    @abstractmethod
    def check_order(self, order: Order, context: Dict[str, Any]) -> tuple[RiskCheckResult, str]:
        """检查订单风险"""
        pass
    
    @abstractmethod
    def check_position(self, position: Position, context: Dict[str, Any]) -> tuple[RiskCheckResult, str]:
        """检查持仓风险"""
        pass


class PositionSizeRule(RiskRule):
    """持仓规模规则"""
    
    def __init__(self, max_position_size: float, symbol: Optional[str] = None):
        super().__init__(f"PositionSize_{symbol or 'Global'}")
        self.max_position_size = max_position_size
        self.symbol = symbol
    
    def check_order(self, order: Order, context: Dict[str, Any]) -> tuple[RiskCheckResult, str]:
        """检查订单是否会导致持仓超限"""
        if self.symbol and order.symbol != self.symbol:
            return RiskCheckResult.PASS, ""
        
        current_positions = context.get('positions', {})
        current_position = current_positions.get(order.symbol, 0.0)
        
        # 计算新持仓
        if order.side == OrderSide.BUY:
            new_position = current_position + order.quantity
        else:
            new_position = current_position - order.quantity
        
        if abs(new_position) > self.max_position_size:
            return RiskCheckResult.REJECT, f"持仓将超过限制: {abs(new_position)} > {self.max_position_size}"
        
        if abs(new_position) > self.max_position_size * 0.8:
            return RiskCheckResult.WARNING, f"持仓接近限制: {abs(new_position)} / {self.max_position_size}"
        
        return RiskCheckResult.PASS, ""
    
    def check_position(self, position: Position, context: Dict[str, Any]) -> tuple[RiskCheckResult, str]:
        """检查当前持仓"""
        if self.symbol and position.symbol != self.symbol:
            return RiskCheckResult.PASS, ""
        
        if abs(position.quantity) > self.max_position_size:
            return RiskCheckResult.REJECT, f"持仓超过限制: {abs(position.quantity)} > {self.max_position_size}"
        
        return RiskCheckResult.PASS, ""


class DailyLossRule(RiskRule):
    """日损失限制规则"""
    
    def __init__(self, max_daily_loss: float):
        super().__init__("DailyLoss")
        self.max_daily_loss = max_daily_loss
    
    def check_order(self, order: Order, context: Dict[str, Any]) -> tuple[RiskCheckResult, str]:
        """检查日损失"""
        daily_pnl = context.get('daily_pnl', 0.0)
        
        if daily_pnl < -self.max_daily_loss:
            return RiskCheckResult.REJECT, f"日损失已达限制: {daily_pnl} < -{self.max_daily_loss}"
        
        if daily_pnl < -self.max_daily_loss * 0.8:
            return RiskCheckResult.WARNING, f"日损失接近限制: {daily_pnl} / -{self.max_daily_loss}"
        
        return RiskCheckResult.PASS, ""
    
    def check_position(self, position: Position, context: Dict[str, Any]) -> tuple[RiskCheckResult, str]:
        """持仓检查不适用于日损失规则"""
        return RiskCheckResult.PASS, ""


class OrderSizeRule(RiskRule):
    """单笔订单规模规则"""
    
    def __init__(self, max_order_size: float, max_order_value: float = 0.0):
        super().__init__("OrderSize")
        self.max_order_size = max_order_size
        self.max_order_value = max_order_value
    
    def check_order(self, order: Order, context: Dict[str, Any]) -> tuple[RiskCheckResult, str]:
        """检查订单规模"""
        # 检查数量
        if order.quantity > self.max_order_size:
            return RiskCheckResult.REJECT, f"订单数量超过限制: {order.quantity} > {self.max_order_size}"
        
        # 检查金额
        if self.max_order_value > 0 and order.price:
            order_value = order.quantity * order.price
            if order_value > self.max_order_value:
                return RiskCheckResult.REJECT, f"订单金额超过限制: {order_value} > {self.max_order_value}"
        
        return RiskCheckResult.PASS, ""
    
    def check_position(self, position: Position, context: Dict[str, Any]) -> tuple[RiskCheckResult, str]:
        """持仓检查不适用于订单规模规则"""
        return RiskCheckResult.PASS, ""


class RiskEngine:
    """风险引擎"""
    
    def __init__(self):
        self.rules: List[RiskRule] = []
        self.limits: Dict[str, RiskLimit] = {}
        self.events: List[RiskEvent] = []
        self.enabled = True
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_checks': 0,
            'passed_checks': 0,
            'warning_checks': 0,
            'rejected_checks': 0,
            'start_time': datetime.now(timezone.utc)
        }
        
        logger.info("🛡️ 风险引擎初始化完成")
    
    def add_rule(self, rule: RiskRule):
        """添加风险规则"""
        with self._lock:
            self.rules.append(rule)
            logger.info(f"📋 添加风险规则: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除风险规则"""
        with self._lock:
            self.rules = [rule for rule in self.rules if rule.name != rule_name]
            logger.info(f"🗑️ 移除风险规则: {rule_name}")
    
    def add_limit(self, limit: RiskLimit):
        """添加风险限制"""
        with self._lock:
            self.limits[limit.name] = limit
            logger.info(f"⚖️ 添加风险限制: {limit.name} = {limit.max_value}")
    
    def update_limit_value(self, limit_name: str, current_value: float):
        """更新限制当前值"""
        with self._lock:
            if limit_name in self.limits:
                self.limits[limit_name].current_value = current_value
    
    def check_order_risk(self, order: Order, context: Dict[str, Any]) -> tuple[RiskCheckResult, List[str]]:
        """检查订单风险"""
        if not self.enabled:
            return RiskCheckResult.PASS, []
        
        with self._lock:
            self.stats['total_checks'] += 1
            
            messages = []
            overall_result = RiskCheckResult.PASS
            
            # 执行所有规则检查
            for rule in self.rules:
                if not rule.enabled:
                    continue
                
                try:
                    result, message = rule.check_order(order, context)
                    
                    if result == RiskCheckResult.REJECT:
                        overall_result = RiskCheckResult.REJECT
                        messages.append(f"[{rule.name}] {message}")
                        
                        # 记录风险事件
                        self._record_risk_event(
                            event_type="order_rejected",
                            risk_level=RiskLevel.HIGH,
                            message=f"订单被拒绝: {message}",
                            order_id=order.order_id,
                            symbol=order.symbol
                        )
                        
                    elif result == RiskCheckResult.WARNING:
                        if overall_result != RiskCheckResult.REJECT:
                            overall_result = RiskCheckResult.WARNING
                        messages.append(f"[{rule.name}] {message}")
                        
                        # 记录风险事件
                        self._record_risk_event(
                            event_type="order_warning",
                            risk_level=RiskLevel.MEDIUM,
                            message=f"订单风险警告: {message}",
                            order_id=order.order_id,
                            symbol=order.symbol
                        )
                
                except Exception as e:
                    logger.error(f"风险规则检查错误 {rule.name}: {e}")
                    messages.append(f"[{rule.name}] 检查错误: {e}")
            
            # 检查风险限制
            for limit in self.limits.values():
                if limit.is_breached():
                    overall_result = RiskCheckResult.REJECT
                    messages.append(f"[{limit.name}] 超过风险限制: {limit.current_value} > {limit.max_value}")
            
            # 更新统计
            if overall_result == RiskCheckResult.PASS:
                self.stats['passed_checks'] += 1
            elif overall_result == RiskCheckResult.WARNING:
                self.stats['warning_checks'] += 1
            else:
                self.stats['rejected_checks'] += 1
            
            return overall_result, messages
    
    def check_position_risk(self, positions: List[Position], context: Dict[str, Any]) -> List[RiskEvent]:
        """检查持仓风险"""
        risk_events = []
        
        with self._lock:
            for position in positions:
                for rule in self.rules:
                    if not rule.enabled:
                        continue
                    
                    try:
                        result, message = rule.check_position(position, context)
                        
                        if result in [RiskCheckResult.REJECT, RiskCheckResult.WARNING]:
                            risk_level = RiskLevel.HIGH if result == RiskCheckResult.REJECT else RiskLevel.MEDIUM
                            
                            event = self._record_risk_event(
                                event_type="position_risk",
                                risk_level=risk_level,
                                message=f"持仓风险: {message}",
                                symbol=position.symbol
                            )
                            risk_events.append(event)
                    
                    except Exception as e:
                        logger.error(f"持仓风险检查错误 {rule.name}: {e}")
        
        return risk_events
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """获取风险摘要"""
        with self._lock:
            # 计算限制使用率
            limit_summary = {}
            for name, limit in self.limits.items():
                limit_summary[name] = {
                    'current': limit.current_value,
                    'max': limit.max_value,
                    'utilization': limit.utilization_rate,
                    'remaining': limit.remaining_capacity,
                    'breached': limit.is_breached()
                }
            
            # 最近风险事件
            recent_events = [
                {
                    'type': event.event_type,
                    'level': event.risk_level.value,
                    'message': event.message,
                    'timestamp': event.timestamp.isoformat(),
                    'symbol': event.symbol
                }
                for event in self.events[-10:]  # 最近10个事件
            ]
            
            return {
                'engine_enabled': self.enabled,
                'active_rules': len([r for r in self.rules if r.enabled]),
                'total_rules': len(self.rules),
                'limits': limit_summary,
                'recent_events': recent_events,
                'statistics': {
                    **self.stats,
                    'uptime_hours': (datetime.now(timezone.utc) - self.stats['start_time']).total_seconds() / 3600,
                    'rejection_rate': self.stats['rejected_checks'] / max(self.stats['total_checks'], 1)
                }
            }
    
    def _record_risk_event(self, event_type: str, risk_level: RiskLevel, message: str, 
                          order_id: Optional[str] = None, symbol: Optional[str] = None) -> RiskEvent:
        """记录风险事件"""
        event = RiskEvent(
            event_type=event_type,
            risk_level=risk_level,
            message=message,
            order_id=order_id,
            symbol=symbol
        )
        
        self.events.append(event)
        
        # 保持事件列表大小
        if len(self.events) > 1000:
            self.events = self.events[-500:]  # 保留最近500个事件
        
        # 记录日志
        log_level = {
            RiskLevel.LOW: logging.INFO,
            RiskLevel.MEDIUM: logging.WARNING,
            RiskLevel.HIGH: logging.ERROR,
            RiskLevel.CRITICAL: logging.CRITICAL
        }.get(risk_level, logging.INFO)
        
        logger.log(log_level, f"🚨 风险事件: {message}")
        
        return event


class RiskManager:
    """企业级风险管理器"""
    
    def __init__(self):
        self.pre_trade_engine = RiskEngine()
        self.post_trade_engine = RiskEngine()
        
        # 默认规则
        self._setup_default_rules()
        
        logger.info("🛡️ 风险管理器初始化完成")
    
    def _setup_default_rules(self):
        """设置默认风险规则"""
        # 添加基础风险规则
        self.pre_trade_engine.add_rule(PositionSizeRule(max_position_size=10000))
        self.pre_trade_engine.add_rule(DailyLossRule(max_daily_loss=50000))
        self.pre_trade_engine.add_rule(OrderSizeRule(max_order_size=1000, max_order_value=100000))
    
    def check_pre_trade_risk(self, order: Order, context: Dict[str, Any]) -> tuple[bool, List[str]]:
        """交易前风险检查"""
        result, messages = self.pre_trade_engine.check_order_risk(order, context)
        
        approved = result != RiskCheckResult.REJECT
        
        if not approved:
            logger.warning(f"🚫 订单被风控拒绝: {order.order_id}")
            for msg in messages:
                logger.warning(f"   - {msg}")
        elif result == RiskCheckResult.WARNING:
            logger.warning(f"⚠️ 订单风险警告: {order.order_id}")
            for msg in messages:
                logger.warning(f"   - {msg}")
        
        return approved, messages
    
    def monitor_post_trade_risk(self, positions: List[Position], context: Dict[str, Any]) -> List[RiskEvent]:
        """交易后风险监控"""
        return self.post_trade_engine.check_position_risk(positions, context)
    
    def add_pre_trade_rule(self, rule: RiskRule):
        """添加交易前规则"""
        self.pre_trade_engine.add_rule(rule)
    
    def add_post_trade_rule(self, rule: RiskRule):
        """添加交易后规则"""
        self.post_trade_engine.add_rule(rule)
    
    def get_comprehensive_risk_report(self) -> Dict[str, Any]:
        """获取综合风险报告"""
        return {
            'pre_trade_risk': self.pre_trade_engine.get_risk_summary(),
            'post_trade_risk': self.post_trade_engine.get_risk_summary(),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
