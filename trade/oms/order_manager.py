"""
企业级订单管理系统 (OMS)
负责订单的完整生命周期管理，包括状态机、路由、执行质量监控
"""
import logging
import threading
import time
from typing import Dict, List, Optional, Callable, Any, Set
from datetime import datetime, timezone
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import uuid

from ..core.base import Order, OrderStatus, OrderType, OrderSide, TimeInForce
from ..gateway.broker_gateway import BrokerGateway

logger = logging.getLogger(__name__)


@dataclass
class OrderExecution:
    """订单执行记录"""
    execution_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    order_id: str = ""
    fill_quantity: float = 0.0
    fill_price: float = 0.0
    execution_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    commission: float = 0.0
    commission_currency: str = "USD"
    exchange: str = ""
    side: OrderSide = OrderSide.BUY
    
    @property
    def fill_value(self) -> float:
        """成交金额"""
        return self.fill_quantity * self.fill_price


@dataclass
class OrderStatistics:
    """订单统计信息"""
    total_orders: int = 0
    filled_orders: int = 0
    cancelled_orders: int = 0
    rejected_orders: int = 0
    partial_fills: int = 0
    
    total_fill_value: float = 0.0
    total_commission: float = 0.0
    
    avg_fill_time: float = 0.0
    avg_slippage: float = 0.0
    
    def update_from_order(self, order: Order):
        """从订单更新统计"""
        self.total_orders += 1
        
        if order.status == OrderStatus.FILLED:
            self.filled_orders += 1
            if order.avg_fill_price and order.filled_quantity:
                self.total_fill_value += order.avg_fill_price * order.filled_quantity
        elif order.status == OrderStatus.CANCELLED:
            self.cancelled_orders += 1
        elif order.status == OrderStatus.REJECTED:
            self.rejected_orders += 1
        elif order.status == OrderStatus.PARTIALLY_FILLED:
            self.partial_fills += 1
    
    @property
    def fill_rate(self) -> float:
        """成交率"""
        if self.total_orders == 0:
            return 0.0
        return (self.filled_orders + self.partial_fills) / self.total_orders
    
    @property
    def rejection_rate(self) -> float:
        """拒绝率"""
        if self.total_orders == 0:
            return 0.0
        return self.rejected_orders / self.total_orders


class OrderStateMachine:
    """订单状态机"""
    
    # 定义状态转换规则
    VALID_TRANSITIONS = {
        OrderStatus.PENDING_NEW: [
            OrderStatus.PENDING_SUBMIT,
            OrderStatus.REJECTED,
            OrderStatus.CANCELLED
        ],
        OrderStatus.PENDING_SUBMIT: [
            OrderStatus.SUBMITTED,
            OrderStatus.PRE_SUBMITTED,
            OrderStatus.REJECTED,
            OrderStatus.CANCELLED
        ],
        OrderStatus.PRE_SUBMITTED: [
            OrderStatus.SUBMITTED,
            OrderStatus.REJECTED,
            OrderStatus.CANCELLED
        ],
        OrderStatus.SUBMITTED: [
            OrderStatus.PARTIALLY_FILLED,
            OrderStatus.FILLED,
            OrderStatus.PENDING_CANCEL,
            OrderStatus.CANCELLED,
            OrderStatus.REJECTED
        ],
        OrderStatus.PARTIALLY_FILLED: [
            OrderStatus.FILLED,
            OrderStatus.PENDING_CANCEL,
            OrderStatus.CANCELLED
        ],
        OrderStatus.PENDING_CANCEL: [
            OrderStatus.CANCELLED,
            OrderStatus.FILLED  # 可能在取消前完全成交
        ],
        # 终态
        OrderStatus.FILLED: [],
        OrderStatus.CANCELLED: [],
        OrderStatus.REJECTED: [],
        OrderStatus.API_CANCELLED: [],
        OrderStatus.INACTIVE: []
    }
    
    @classmethod
    def can_transition(cls, from_status: OrderStatus, to_status: OrderStatus) -> bool:
        """检查状态转换是否有效"""
        return to_status in cls.VALID_TRANSITIONS.get(from_status, [])
    
    @classmethod
    def get_valid_transitions(cls, current_status: OrderStatus) -> List[OrderStatus]:
        """获取当前状态的有效转换"""
        return cls.VALID_TRANSITIONS.get(current_status, [])
    
    @classmethod
    def is_terminal_state(cls, status: OrderStatus) -> bool:
        """检查是否为终态"""
        return len(cls.VALID_TRANSITIONS.get(status, [])) == 0


class OrderRouter:
    """订单路由器"""
    
    def __init__(self):
        self.gateways: Dict[str, BrokerGateway] = {}
        self.routing_rules: Dict[str, str] = {}  # symbol -> gateway_name
        self.default_gateway: Optional[str] = None
    
    def add_gateway(self, name: str, gateway: BrokerGateway):
        """添加网关"""
        self.gateways[name] = gateway
        if self.default_gateway is None:
            self.default_gateway = name
        logger.info(f"📡 添加交易网关: {name}")
    
    def set_routing_rule(self, symbol: str, gateway_name: str):
        """设置路由规则"""
        if gateway_name not in self.gateways:
            raise ValueError(f"网关不存在: {gateway_name}")
        self.routing_rules[symbol] = gateway_name
        logger.info(f"🔀 设置路由规则: {symbol} -> {gateway_name}")
    
    def route_order(self, order: Order) -> Optional[BrokerGateway]:
        """路由订单到合适的网关"""
        # 检查特定符号的路由规则
        if order.symbol in self.routing_rules:
            gateway_name = self.routing_rules[order.symbol]
            return self.gateways.get(gateway_name)
        
        # 使用默认网关
        if self.default_gateway:
            return self.gateways.get(self.default_gateway)
        
        return None


class ExecutionQualityMonitor:
    """执行质量监控器"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.executions: deque = deque(maxlen=window_size)
        self.slippage_data: deque = deque(maxlen=window_size)
        self.fill_times: deque = deque(maxlen=window_size)
        self._lock = threading.Lock()
    
    def record_execution(self, order: Order, execution: OrderExecution, expected_price: Optional[float] = None):
        """记录执行信息"""
        with self._lock:
            self.executions.append(execution)
            
            # 计算滑点
            if expected_price and execution.fill_price:
                slippage = abs(execution.fill_price - expected_price) / expected_price
                self.slippage_data.append(slippage)
            
            # 计算执行时间
            if order.submitted_time:
                fill_time = (execution.execution_time - order.submitted_time).total_seconds()
                self.fill_times.append(fill_time)
    
    def get_execution_quality_report(self) -> Dict[str, Any]:
        """获取执行质量报告"""
        with self._lock:
            if not self.executions:
                return {}
            
            total_volume = sum(exec.fill_quantity for exec in self.executions)
            total_value = sum(exec.fill_value for exec in self.executions)
            total_commission = sum(exec.commission for exec in self.executions)
            
            avg_slippage = sum(self.slippage_data) / len(self.slippage_data) if self.slippage_data else 0.0
            avg_fill_time = sum(self.fill_times) / len(self.fill_times) if self.fill_times else 0.0
            
            return {
                'total_executions': len(self.executions),
                'total_volume': total_volume,
                'total_value': total_value,
                'total_commission': total_commission,
                'avg_slippage_bps': avg_slippage * 10000,  # 基点
                'avg_fill_time_seconds': avg_fill_time,
                'commission_rate_bps': (total_commission / total_value * 10000) if total_value > 0 else 0.0
            }


class OrderManager:
    """企业级订单管理器"""
    
    def __init__(self):
        # 订单存储
        self.orders: Dict[str, Order] = {}
        self.executions: Dict[str, List[OrderExecution]] = defaultdict(list)
        
        # 组件
        self.router = OrderRouter()
        self.quality_monitor = ExecutionQualityMonitor()
        self.statistics = OrderStatistics()
        
        # 事件处理
        self.event_handlers: Dict[str, List[Callable]] = defaultdict(list)
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 订单索引
        self.orders_by_symbol: Dict[str, Set[str]] = defaultdict(set)
        self.orders_by_status: Dict[OrderStatus, Set[str]] = defaultdict(set)
        self.active_orders: Set[str] = set()
        
        logger.info("🏗️ 订单管理系统初始化完成")
    
    def add_gateway(self, name: str, gateway: BrokerGateway):
        """添加交易网关"""
        self.router.add_gateway(name, gateway)
        
        # 设置网关事件处理
        gateway.add_event_handler('order_status_update', self._on_order_status_update)
        gateway.add_event_handler('order_filled', self._on_order_filled)
    
    def create_order(self, symbol: str, side: OrderSide, quantity: float, 
                    order_type: OrderType = OrderType.MARKET, 
                    price: Optional[float] = None,
                    stop_price: Optional[float] = None,
                    time_in_force: TimeInForce = TimeInForce.DAY,
                    **kwargs) -> Order:
        """创建订单"""
        order = Order(
            symbol=symbol,
            side=side,
            quantity=quantity,
            order_type=order_type,
            price=price,
            stop_price=stop_price,
            time_in_force=time_in_force,
            **kwargs
        )
        
        with self._lock:
            self.orders[order.order_id] = order
            self._update_indices(order)
        
        logger.info(f"📋 创建订单: {order.order_id} {symbol} {side.value} {quantity}")
        self._emit_event('order_created', order)
        
        return order
    
    def submit_order(self, order_id: str) -> bool:
        """提交订单"""
        with self._lock:
            order = self.orders.get(order_id)
            if not order:
                logger.error(f"❌ 订单不存在: {order_id}")
                return False
            
            # 检查状态转换
            if not OrderStateMachine.can_transition(order.status, OrderStatus.PENDING_SUBMIT):
                logger.error(f"❌ 无效的状态转换: {order.status} -> {OrderStatus.PENDING_SUBMIT}")
                return False
            
            # 更新状态
            order.update_status(OrderStatus.PENDING_SUBMIT)
            self._update_indices(order)
            
            # 路由订单
            gateway = self.router.route_order(order)
            if not gateway:
                logger.error(f"❌ 无法路由订单: {order_id}")
                order.update_status(OrderStatus.REJECTED)
                order.error_message = "无可用网关"
                return False
            
            # 提交到网关
            success = gateway.submit_order(order)
            if success:
                order.update_status(OrderStatus.SUBMITTED)
                self.active_orders.add(order_id)
                self.statistics.update_from_order(order)
                logger.info(f"✅ 订单提交成功: {order_id}")
            else:
                order.update_status(OrderStatus.REJECTED)
                logger.error(f"❌ 订单提交失败: {order_id}")
            
            self._update_indices(order)
            self._emit_event('order_submitted' if success else 'order_rejected', order)
            
            return success
    
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        with self._lock:
            order = self.orders.get(order_id)
            if not order:
                logger.error(f"❌ 订单不存在: {order_id}")
                return False
            
            if not order.is_active:
                logger.warning(f"⚠️ 订单不是活跃状态: {order_id} ({order.status})")
                return False
            
            # 更新状态
            order.update_status(OrderStatus.PENDING_CANCEL)
            self._update_indices(order)
            
            # 通过网关取消
            gateway = self.router.route_order(order)
            if gateway:
                success = gateway.cancel_order(order_id)
                if success:
                    logger.info(f"🚫 订单取消请求已发送: {order_id}")
                    self._emit_event('order_cancel_requested', order)
                    return True
            
            logger.error(f"❌ 订单取消失败: {order_id}")
            return False
    
    def get_order(self, order_id: str) -> Optional[Order]:
        """获取订单"""
        return self.orders.get(order_id)
    
    def get_orders_by_symbol(self, symbol: str) -> List[Order]:
        """按符号获取订单"""
        order_ids = self.orders_by_symbol.get(symbol, set())
        return [self.orders[oid] for oid in order_ids if oid in self.orders]
    
    def get_orders_by_status(self, status: OrderStatus) -> List[Order]:
        """按状态获取订单"""
        order_ids = self.orders_by_status.get(status, set())
        return [self.orders[oid] for oid in order_ids if oid in self.orders]
    
    def get_active_orders(self) -> List[Order]:
        """获取活跃订单"""
        return [self.orders[oid] for oid in self.active_orders if oid in self.orders]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        quality_report = self.quality_monitor.get_execution_quality_report()
        
        return {
            'order_statistics': {
                'total_orders': self.statistics.total_orders,
                'filled_orders': self.statistics.filled_orders,
                'cancelled_orders': self.statistics.cancelled_orders,
                'rejected_orders': self.statistics.rejected_orders,
                'fill_rate': self.statistics.fill_rate,
                'rejection_rate': self.statistics.rejection_rate
            },
            'execution_quality': quality_report,
            'active_orders_count': len(self.active_orders),
            'total_orders_count': len(self.orders)
        }
    
    def add_event_handler(self, event_type: str, handler: Callable):
        """添加事件处理器"""
        self.event_handlers[event_type].append(handler)
    
    def _update_indices(self, order: Order):
        """更新订单索引"""
        # 更新符号索引
        self.orders_by_symbol[order.symbol].add(order.order_id)
        
        # 更新状态索引
        # 先清理旧状态
        for status, order_set in self.orders_by_status.items():
            order_set.discard(order.order_id)
        
        # 添加新状态
        self.orders_by_status[order.status].add(order.order_id)
        
        # 更新活跃订单集合
        if order.is_active:
            self.active_orders.add(order.order_id)
        else:
            self.active_orders.discard(order.order_id)
    
    def _emit_event(self, event_type: str, data: Any):
        """触发事件"""
        for handler in self.event_handlers[event_type]:
            try:
                handler(data)
            except Exception as e:
                logger.error(f"事件处理器错误 {event_type}: {e}")
    
    def _on_order_status_update(self, order: Order):
        """处理订单状态更新"""
        with self._lock:
            if order.order_id in self.orders:
                self.orders[order.order_id] = order
                self._update_indices(order)
                self.statistics.update_from_order(order)
                self._emit_event('order_status_changed', order)
    
    def _on_order_filled(self, data: Dict[str, Any]):
        """处理订单成交"""
        order_id = data.get('order_id')
        fill_info = data.get('fill')
        
        if order_id and order_id in self.orders:
            order = self.orders[order_id]
            
            # 创建执行记录
            execution = OrderExecution(
                order_id=order_id,
                fill_quantity=float(fill_info.execution.shares),
                fill_price=float(fill_info.execution.price),
                execution_time=datetime.now(timezone.utc),
                commission=float(fill_info.commissionReport.commission) if fill_info.commissionReport else 0.0,
                side=order.side
            )
            
            self.executions[order_id].append(execution)
            self.quality_monitor.record_execution(order, execution, order.price)
            
            logger.info(f"💰 订单成交: {order_id} {execution.fill_quantity}@{execution.fill_price}")
            self._emit_event('order_execution', {'order': order, 'execution': execution})
