"""
回测引擎 - 历史数据上的策略测试
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import logging

from ..core.base import (
    Order, Position, Signal, MarketData, OrderStatus, OrderSide, OrderType,
    Strategy, Broker, Event, EventType
)
from ..engine.trading_engine import TradingEngine


logger = logging.getLogger(__name__)


@dataclass
class BacktestConfig:
    """回测配置"""
    initial_capital: float = 100000.0
    commission_per_share: float = 0.001
    slippage_pct: float = 0.0001
    start_date: datetime = None
    end_date: datetime = None
    benchmark_symbol: str = "SPY"


@dataclass
class BacktestResult:
    """回测结果"""
    total_return: float = 0.0
    annual_return: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    trades: List[Dict] = field(default_factory=list)
    equity_curve: pd.DataFrame = field(default_factory=pd.DataFrame)
    daily_returns: pd.Series = field(default_factory=pd.Series)


class SimulatedBroker(Broker):
    """模拟经纪商"""

    def __init__(self, initial_capital: float, commission: float = 0.001, slippage: float = 0.0001):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.commission = commission
        self.slippage = slippage

        self.positions: Dict[str, Position] = {}
        self.orders: Dict[str, Order] = {}
        self.trades: List[Dict] = []
        self.order_id_counter = 0

        self.current_prices: Dict[str, float] = {}

    def submit_order(self, order: Order) -> str:
        """提交订单"""
        self.order_id_counter += 1
        order_id = f"ORDER_{self.order_id_counter}"
        order.id = order_id
        order.status = OrderStatus.SUBMITTED
        self.orders[order_id] = order
        return order_id

    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        if order_id in self.orders:
            self.orders[order_id].status = OrderStatus.CANCELLED
            return True
        return False

    def update_market_data(self, market_data: MarketData):
        """更新市场数据"""
        self.current_prices[market_data.symbol] = market_data.close

        # 检查并执行订单
        self._process_pending_orders(market_data)

        # 更新仓位价格
        if market_data.symbol in self.positions:
            self.positions[market_data.symbol].current_price = market_data.close

    def _process_pending_orders(self, market_data: MarketData):
        """处理待处理订单"""
        symbol = market_data.symbol
        current_price = market_data.close

        orders_to_remove = []
        for order_id, order in self.orders.items():
            if order.symbol != symbol or order.status != OrderStatus.SUBMITTED:
                continue

            should_fill = False
            fill_price = current_price

            if order.order_type == OrderType.MARKET:
                should_fill = True
                # 考虑滑点
                if order.side == OrderSide.BUY:
                    fill_price *= (1 + self.slippage)
                else:
                    fill_price *= (1 - self.slippage)

            elif order.order_type == OrderType.LIMIT:
                if order.side == OrderSide.BUY and current_price <= order.price:
                    should_fill = True
                    fill_price = order.price
                elif order.side == OrderSide.SELL and current_price >= order.price:
                    should_fill = True
                    fill_price = order.price

            if should_fill:
                self._execute_order(order, fill_price)
                orders_to_remove.append(order_id)

        # 移除已执行的订单
        for order_id in orders_to_remove:
            del self.orders[order_id]

    def _execute_order(self, order: Order, fill_price: float):
        """执行订单"""
        symbol = order.symbol
        quantity = order.quantity if order.side == OrderSide.BUY else -order.quantity

        # 计算手续费
        commission = abs(quantity) * self.commission

        # 更新资金
        cost = quantity * fill_price + commission
        self.current_capital -= cost

        # 更新仓位
        if symbol not in self.positions:
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=0,
                avg_cost=0.0,
                current_price=fill_price
            )

        pos = self.positions[symbol]

        # 计算新的平均成本
        if pos.quantity == 0:
            pos.avg_cost = fill_price
        elif (pos.quantity > 0 and quantity > 0) or (pos.quantity < 0 and quantity < 0):
            # 加仓
            total_cost = pos.quantity * pos.avg_cost + quantity * fill_price
            pos.avg_cost = total_cost / (pos.quantity + quantity)

        pos.quantity += quantity
        pos.current_price = fill_price

        # 记录交易
        trade = {
            'timestamp': datetime.now(),
            'symbol': symbol,
            'side': order.side.value,
            'quantity': abs(quantity),
            'price': fill_price,
            'commission': commission,
            'order_id': order.id
        }
        self.trades.append(trade)

        # 更新订单状态
        order.status = OrderStatus.FILLED
        order.filled_quantity = order.quantity
        order.filled_price = fill_price

    def get_positions(self) -> List[Position]:
        """获取持仓"""
        return [pos for pos in self.positions.values() if pos.quantity != 0]

    def get_account_info(self) -> Dict[str, float]:
        """获取账户信息"""
        # 计算总市值
        total_market_value = sum(
            pos.quantity * pos.current_price
            for pos in self.positions.values()
        )

        net_liquidation = self.current_capital + total_market_value

        return {
            'TotalCashValue': self.current_capital,
            'NetLiquidation': net_liquidation,
            'GrossPositionValue': total_market_value
        }

    def get_portfolio_value(self) -> float:
        """获取组合总价值"""
        return self.get_account_info()['NetLiquidation']


class BacktestEngine:
    """回测引擎"""

    def __init__(self, config: BacktestConfig):
        self.config = config
        self.broker = SimulatedBroker(
            config.initial_capital,
            config.commission_per_share,
            config.slippage_pct
        )

        # 风险配置
        risk_config = {
            'max_position_size': 1000,
            'max_portfolio_risk': 0.2,
            'max_single_position_risk': 0.1,
            'stop_loss_pct': 0.05
        }

        self.trading_engine = TradingEngine(self.broker, risk_config)
        self.equity_curve = []
        self.daily_returns = []

    def run(self, strategy: Strategy, data: pd.DataFrame) -> BacktestResult:
        """运行回测"""
        logger.info("开始回测...")

        # 添加策略
        self.trading_engine.add_strategy(strategy)
        self.trading_engine.start()

        # 初始化权益曲线
        initial_value = self.config.initial_capital
        self.equity_curve.append({
            'date': data.index[0],
            'portfolio_value': initial_value
        })

        # 逐日回测
        for i, (timestamp, row) in enumerate(data.iterrows()):
            # 创建市场数据
            market_data = MarketData(
                symbol=strategy.name,  # 简化处理，使用策略名作为symbol
                timestamp=timestamp,
                open=row['open'],
                high=row['high'],
                low=row['low'],
                close=row['close'],
                volume=int(row['volume'])
            )

            # 更新经纪商市场数据
            self.broker.update_market_data(market_data)

            # 传递市场数据给交易引擎
            market_event = Event(EventType.MARKET_DATA, timestamp, market_data)
            self.trading_engine.process_event(market_event)

            # 记录权益曲线
            portfolio_value = self.broker.get_portfolio_value()
            self.equity_curve.append({
                'date': timestamp,
                'portfolio_value': portfolio_value
            })

            # 计算日收益率
            if len(self.equity_curve) > 1:
                prev_value = self.equity_curve[-2]['portfolio_value']
                daily_return = (portfolio_value - prev_value) / prev_value
                self.daily_returns.append(daily_return)

        self.trading_engine.stop()

        # 生成回测结果
        result = self._generate_results()
        logger.info("回测完成")

        return result

    def _generate_results(self) -> BacktestResult:
        """生成回测结果"""
        equity_df = pd.DataFrame(self.equity_curve)
        equity_df.set_index('date', inplace=True)

        returns_series = pd.Series(self.daily_returns,
                                 index=equity_df.index[1:])

        # 计算基础指标
        total_return = (equity_df['portfolio_value'].iloc[-1] -
                       equity_df['portfolio_value'].iloc[0]) / equity_df['portfolio_value'].iloc[0]

        # 年化收益率
        trading_days = len(equity_df)
        annual_return = (1 + total_return) ** (252 / trading_days) - 1

        # 夏普比率
        sharpe_ratio = returns_series.mean() / returns_series.std() * np.sqrt(252) if returns_series.std() > 0 else 0

        # 最大回撤
        peak = equity_df['portfolio_value'].expanding().max()
        drawdown = (equity_df['portfolio_value'] - peak) / peak
        max_drawdown = drawdown.min()

        # 交易统计
        trades = self.broker.trades
        winning_trades = [t for t in trades if self._calculate_trade_pnl(t) > 0]
        win_rate = len(winning_trades) / len(trades) if trades else 0

        # 盈亏比
        gross_profit = sum(self._calculate_trade_pnl(t) for t in winning_trades)
        gross_loss = abs(sum(self._calculate_trade_pnl(t) for t in trades if self._calculate_trade_pnl(t) < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

        return BacktestResult(
            total_return=total_return,
            annual_return=annual_return,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
            trades=trades,
            equity_curve=equity_df,
            daily_returns=returns_series
        )

    def _calculate_trade_pnl(self, trade: Dict) -> float:
        """计算单笔交易盈亏"""
        # 简化处理，实际需要配对买卖交易
        return 0.0


class PerformanceAnalyzer:
    """性能分析器"""

    @staticmethod
    def calculate_metrics(result: BacktestResult, benchmark_returns: Optional[pd.Series] = None) -> Dict[str, float]:
        """计算详细性能指标"""
        returns = result.daily_returns

        metrics = {
            'Total Return': result.total_return,
            'Annual Return': result.annual_return,
            'Sharpe Ratio': result.sharpe_ratio,
            'Max Drawdown': result.max_drawdown,
            'Win Rate': result.win_rate,
            'Profit Factor': result.profit_factor,
            'Volatility': returns.std() * np.sqrt(252),
            'Skewness': returns.skew(),
            'Kurtosis': returns.kurtosis(),
            'Best Day': returns.max(),
            'Worst Day': returns.min(),
            'Total Trades': len(result.trades)
        }

        # 如果有基准数据，计算相对指标
        if benchmark_returns is not None:
            metrics['Beta'] = PerformanceAnalyzer._calculate_beta(returns, benchmark_returns)
            metrics['Alpha'] = PerformanceAnalyzer._calculate_alpha(returns, benchmark_returns, metrics['Beta'])
            metrics['Information Ratio'] = PerformanceAnalyzer._calculate_information_ratio(returns, benchmark_returns)

        return metrics

    @staticmethod
    def _calculate_beta(returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """计算Beta"""
        aligned_returns = returns.align(benchmark_returns, join='inner')
        if len(aligned_returns[0]) < 2:
            return 0.0
        return np.cov(aligned_returns[0], aligned_returns[1])[0, 1] / np.var(aligned_returns[1])

    @staticmethod
    def _calculate_alpha(returns: pd.Series, benchmark_returns: pd.Series, beta: float, risk_free_rate: float = 0.02) -> float:
        """计算Alpha"""
        strategy_return = returns.mean() * 252
        benchmark_return = benchmark_returns.mean() * 252
        return strategy_return - (risk_free_rate + beta * (benchmark_return - risk_free_rate))

    @staticmethod
    def _calculate_information_ratio(returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """计算信息比率"""
        aligned_returns = returns.align(benchmark_returns, join='inner')
        active_returns = aligned_returns[0] - aligned_returns[1]
        if active_returns.std() == 0:
            return 0.0
        return active_returns.mean() / active_returns.std() * np.sqrt(252)

    @staticmethod
    def generate_report(result: BacktestResult, metrics: Dict[str, float]) -> str:
        """生成回测报告"""
        report = f"""
========================================
           回测报告
========================================

基础指标:
- 总收益率: {metrics['Total Return']:.2%}
- 年化收益率: {metrics['Annual Return']:.2%}
- 最大回撤: {metrics['Max Drawdown']:.2%}
- 年化波动率: {metrics['Volatility']:.2%}

风险调整指标:
- 夏普比率: {metrics['Sharpe Ratio']:.3f}
- 偏度: {metrics['Skewness']:.3f}
- 峰度: {metrics['Kurtosis']:.3f}

交易统计:
- 总交易次数: {metrics['Total Trades']:.0f}
- 胜率: {metrics['Win Rate']:.2%}
- 盈亏比: {metrics['Profit Factor']:.3f}
- 最佳单日: {metrics['Best Day']:.2%}
- 最差单日: {metrics['Worst Day']:.2%}

"""

        if 'Beta' in metrics:
            report += f"""
基准比较:
- Beta: {metrics['Beta']:.3f}
- Alpha: {metrics['Alpha']:.3f}
- 信息比率: {metrics['Information Ratio']:.3f}
"""

        report += "========================================"
        return report