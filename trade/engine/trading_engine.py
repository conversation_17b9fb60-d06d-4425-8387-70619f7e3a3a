"""
交易引擎 - 策略执行和风险管理
"""
from typing import Dict, List, Optional, Callable
from datetime import datetime
import logging
from collections import defaultdict
import threading

from ..core.base import (
    Order, Position, Signal, MarketData, OrderStatus, OrderSide, OrderType,
    Strategy, Broker, Event, EventType
)


logger = logging.getLogger(__name__)


class RiskManager:
    """风险管理器"""

    def __init__(self, config: Dict):
        self.config = config
        self.max_position_size = config.get('max_position_size', 1000)
        self.max_portfolio_risk = config.get('max_portfolio_risk', 0.02)  # 2%
        self.max_single_position_risk = config.get('max_single_position_risk', 0.01)  # 1%
        self.max_drawdown = config.get('max_drawdown', 0.1)  # 10%
        self.stop_loss_pct = config.get('stop_loss_pct', 0.05)  # 5%

    def check_position_risk(self, symbol: str, quantity: int, price: float,
                          portfolio_value: float) -> bool:
        """检查单个仓位风险"""
        position_value = abs(quantity) * price
        risk_ratio = position_value / portfolio_value

        if risk_ratio > self.max_single_position_risk:
            logger.warning(f"单个仓位风险过高: {symbol}, 风险比例: {risk_ratio:.3f}")
            return False

        return True

    def check_portfolio_risk(self, positions: List[Position], portfolio_value: float) -> bool:
        """检查组合风险"""
        total_risk = sum(abs(pos.quantity * pos.current_price) for pos in positions)
        risk_ratio = total_risk / portfolio_value

        if risk_ratio > self.max_portfolio_risk:
            logger.warning(f"组合风险过高: {risk_ratio:.3f}")
            return False

        return True

    def calculate_position_size(self, signal_strength: float, price: float,
                              portfolio_value: float) -> int:
        """计算仓位大小"""
        # 基于Kelly公式的简化版本
        risk_per_trade = self.max_single_position_risk
        base_size = int((portfolio_value * risk_per_trade) / price)

        # 根据信号强度调整
        adjusted_size = int(base_size * signal_strength)

        # 不超过最大仓位限制
        return min(adjusted_size, self.max_position_size)

    def should_stop_loss(self, position: Position) -> bool:
        """检查是否应该止损"""
        if position.quantity == 0:
            return False

        current_loss = (position.avg_cost - position.current_price) / position.avg_cost
        if position.quantity < 0:  # 空头
            current_loss = -current_loss

        return current_loss >= self.stop_loss_pct


class PositionManager:
    """仓位管理器"""

    def __init__(self):
        self.positions: Dict[str, Position] = {}
        self._lock = threading.Lock()

    def update_position(self, symbol: str, quantity: int, price: float):
        """更新仓位"""
        with self._lock:
            if symbol not in self.positions:
                self.positions[symbol] = Position(
                    symbol=symbol,
                    quantity=0,
                    avg_cost=0.0,
                    current_price=price
                )

            pos = self.positions[symbol]

            if pos.quantity == 0:
                # 新开仓
                pos.quantity = quantity
                pos.avg_cost = price
            elif (pos.quantity > 0 and quantity > 0) or (pos.quantity < 0 and quantity < 0):
                # 加仓
                total_cost = pos.quantity * pos.avg_cost + quantity * price
                pos.quantity += quantity
                pos.avg_cost = total_cost / pos.quantity if pos.quantity != 0 else 0
            else:
                # 减仓或反向开仓
                if abs(quantity) <= abs(pos.quantity):
                    # 减仓
                    pos.quantity += quantity
                else:
                    # 反向开仓
                    pos.quantity = quantity - pos.quantity
                    pos.avg_cost = price

            pos.current_price = price
            self._calculate_pnl(pos)

    def _calculate_pnl(self, position: Position):
        """计算盈亏"""
        if position.quantity == 0:
            position.unrealized_pnl = 0
        else:
            position.unrealized_pnl = position.quantity * (position.current_price - position.avg_cost)

    def get_position(self, symbol: str) -> Optional[Position]:
        """获取仓位"""
        return self.positions.get(symbol)

    def get_all_positions(self) -> List[Position]:
        """获取所有仓位"""
        return list(self.positions.values())

    def update_market_prices(self, market_data: Dict[str, float]):
        """批量更新市场价格"""
        with self._lock:
            for symbol, price in market_data.items():
                if symbol in self.positions:
                    self.positions[symbol].current_price = price
                    self._calculate_pnl(self.positions[symbol])


class OrderManager:
    """订单管理器"""

    def __init__(self, broker: Broker):
        self.broker = broker
        self.pending_orders: Dict[str, Order] = {}
        self.order_history: List[Order] = []
        self._lock = threading.Lock()

    def submit_order(self, order: Order) -> bool:
        """提交订单"""
        with self._lock:
            try:
                order_id = self.broker.submit_order(order)
                if order_id:
                    order.id = order_id
                    order.status = OrderStatus.SUBMITTED
                    self.pending_orders[order_id] = order
                    logger.info(f"订单提交成功: {order_id} {order.symbol} {order.side.value} {order.quantity}")
                    return True
                else:
                    order.status = OrderStatus.REJECTED
                    self.order_history.append(order)
                    return False
            except Exception as e:
                logger.error(f"订单提交失败: {e}")
                order.status = OrderStatus.REJECTED
                self.order_history.append(order)
                return False

    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        with self._lock:
            if order_id in self.pending_orders:
                try:
                    success = self.broker.cancel_order(order_id)
                    if success:
                        order = self.pending_orders.pop(order_id)
                        order.status = OrderStatus.CANCELLED
                        self.order_history.append(order)
                        logger.info(f"订单取消成功: {order_id}")
                        return True
                except Exception as e:
                    logger.error(f"订单取消失败: {e}")
            return False

    def update_order_status(self, order_id: str, status: OrderStatus,
                          filled_quantity: int = 0, filled_price: float = None):
        """更新订单状态"""
        with self._lock:
            if order_id in self.pending_orders:
                order = self.pending_orders[order_id]
                order.status = status
                order.filled_quantity = filled_quantity

                if filled_price:
                    order.filled_price = filled_price

                if status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                    self.pending_orders.pop(order_id)
                    self.order_history.append(order)

    def get_pending_orders(self) -> List[Order]:
        """获取待处理订单"""
        return list(self.pending_orders.values())


class TradingEngine:
    """交易引擎主类"""

    def __init__(self, broker: Broker, risk_config: Dict):
        self.broker = broker
        self.risk_manager = RiskManager(risk_config)
        self.position_manager = PositionManager()
        self.order_manager = OrderManager(broker)

        self.strategies: Dict[str, Strategy] = {}
        self.is_running = False
        self._event_handlers = defaultdict(list)

        # 绑定事件处理器
        self._setup_event_handlers()

    def _setup_event_handlers(self):
        """设置事件处理器"""
        self.add_event_handler(EventType.MARKET_DATA, self._handle_market_data)
        self.add_event_handler(EventType.SIGNAL, self._handle_signal)
        self.add_event_handler(EventType.FILL, self._handle_fill)

    def add_strategy(self, strategy: Strategy):
        """添加策略"""
        self.strategies[strategy.name] = strategy
        logger.info(f"添加策略: {strategy.name}")

    def add_event_handler(self, event_type: EventType, handler: Callable):
        """添加事件处理器"""
        self._event_handlers[event_type].append(handler)

    def process_event(self, event: Event):
        """处理事件"""
        handlers = self._event_handlers.get(event.type, [])
        for handler in handlers:
            try:
                handler(event)
            except Exception as e:
                logger.error(f"事件处理失败: {e}")

    def _handle_market_data(self, event: Event):
        """处理市场数据事件"""
        market_data: MarketData = event.data

        # 更新仓位的市场价格
        market_prices = {market_data.symbol: market_data.close}
        self.position_manager.update_market_prices(market_prices)

        # 传递给所有策略
        for strategy in self.strategies.values():
            if strategy.is_active:
                try:
                    strategy.on_market_data(market_data)
                except Exception as e:
                    logger.error(f"策略 {strategy.name} 处理市场数据失败: {e}")

    def _handle_signal(self, event: Event):
        """处理交易信号事件"""
        signal: Signal = event.data

        if signal.signal_type == 'HOLD':
            return

        # 获取当前账户信息
        try:
            account_info = self.broker.get_account_info()
            portfolio_value = account_info.get('NetLiquidation', 100000)  # 默认10万
        except Exception as e:
            logger.error(f"获取账户信息失败: {e}")
            return

        # 风险检查
        current_position = self.position_manager.get_position(signal.symbol)
        if current_position and self.risk_manager.should_stop_loss(current_position):
            logger.warning(f"触发止损: {signal.symbol}")
            self._create_stop_loss_order(current_position)
            return

        # 计算订单参数
        if signal.signal_type in ['BUY', 'SELL']:
            self._create_signal_order(signal, portfolio_value)

    def _create_signal_order(self, signal: Signal, portfolio_value: float):
        """根据信号创建订单"""
        try:
            # 获取当前价格（简化处理）
            current_price = signal.metadata.get('price', 100.0)

            # 计算仓位大小
            position_size = self.risk_manager.calculate_position_size(
                signal.strength, current_price, portfolio_value
            )

            if position_size == 0:
                return

            # 风险检查
            if not self.risk_manager.check_position_risk(
                signal.symbol, position_size, current_price, portfolio_value
            ):
                return

            # 创建订单
            side = OrderSide.BUY if signal.signal_type == 'BUY' else OrderSide.SELL
            order = Order(
                id="",
                symbol=signal.symbol,
                side=side,
                order_type=OrderType.MARKET,
                quantity=position_size
            )

            # 提交订单
            self.order_manager.submit_order(order)

        except Exception as e:
            logger.error(f"创建信号订单失败: {e}")

    def _create_stop_loss_order(self, position: Position):
        """创建止损订单"""
        try:
            side = OrderSide.SELL if position.quantity > 0 else OrderSide.BUY
            stop_price = position.avg_cost * (1 - self.risk_manager.stop_loss_pct)
            if position.quantity < 0:
                stop_price = position.avg_cost * (1 + self.risk_manager.stop_loss_pct)

            order = Order(
                id="",
                symbol=position.symbol,
                side=side,
                order_type=OrderType.STOP,
                quantity=abs(position.quantity),
                price=stop_price
            )

            self.order_manager.submit_order(order)

        except Exception as e:
            logger.error(f"创建止损订单失败: {e}")

    def _handle_fill(self, event: Event):
        """处理成交事件"""
        # 更新仓位
        fill_data = event.data
        self.position_manager.update_position(
            fill_data['symbol'],
            fill_data['quantity'],
            fill_data['price']
        )

    def start(self):
        """启动交易引擎"""
        self.is_running = True
        for strategy in self.strategies.values():
            strategy.is_active = True
        logger.info("交易引擎启动")

    def stop(self):
        """停止交易引擎"""
        self.is_running = False
        for strategy in self.strategies.values():
            strategy.is_active = False
        logger.info("交易引擎停止")

    def get_portfolio_summary(self) -> Dict:
        """获取组合摘要"""
        positions = self.position_manager.get_all_positions()
        pending_orders = self.order_manager.get_pending_orders()

        total_value = sum(pos.market_value for pos in positions)
        total_pnl = sum(pos.total_pnl for pos in positions)

        return {
            'total_positions': len(positions),
            'total_market_value': total_value,
            'total_pnl': total_pnl,
            'pending_orders': len(pending_orders),
            'positions': positions,
            'orders': pending_orders
        }