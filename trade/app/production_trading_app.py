"""
生产级量化交易应用 - 专业交易基础设施
专为实际交易环境设计，提供稳定可靠的broker连接和交易执行
"""
import logging
import time
import threading
import signal
import sys
import json
import os
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from pathlib import Path

from ..core.base import Event, EventType
from ..core.config import config
from ..core.realtime_manager import RealtimeDataManager
from ..core.execution_manager import ExecutionManager
from ..data.data_manager import HistoricalDataManager
from ..engine.trading_engine import TradingEngine
from ..backtest.backtest_engine import BacktestEngine
from ..adapters.ib_adapter import IBFrameworkAdapter
# 策略导入已移至独立目录 strategies/
# 使用时需要: from strategies.your_strategy import YourStrategy

logger = logging.getLogger(__name__)


class ProductionTradingApplication:
    """生产级量化交易应用主类"""

    def __init__(self, config_data: Dict):
        self.config = config_data
        self.mode = config_data.get('mode', 'paper')

        # 核心组件
        self.ib_adapter = None
        self.historical_data_manager = None
        self.trading_engine = None
        self.realtime_manager = None
        self.execution_manager = None

        # 状态管理
        self.is_initialized = False
        self.is_running = False
        self.start_time = None

        # 性能监控
        self.stats = {
            'uptime_seconds': 0,
            'connections_established': 0,
            'data_updates_received': 0,
            'orders_processed': 0,
            'errors_encountered': 0
        }

        # 线程管理
        self._shutdown_event = threading.Event()
        self._setup_signal_handlers()

    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"\n⏹️ 收到信号 {signum}，开始优雅关闭...")
            self.stop()
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def initialize(self):
        """初始化应用 - 生产级启动流程"""
        if self.is_initialized:
            logger.warning("应用已初始化")
            return

        try:
            logger.info(f"🚀 初始化量化交易框架 - 模式: {self.mode.upper()}")

            # 1. 初始化IB适配器并验证连接
            logger.info("📡 初始化IB连接...")
            self.ib_adapter = IBFrameworkAdapter()

            if not self.ib_adapter.connect():
                raise ConnectionError("无法连接到IB Gateway")

            self.stats['connections_established'] += 1
            logger.info("✅ IB连接建立成功")

            # 2. 初始化数据管理器
            logger.info("📊 初始化数据管理...")
            self.historical_data_manager = HistoricalDataManager(
                data_provider=self.ib_adapter.data_provider
            )

            # 3. 初始化实时数据管理器
            logger.info("📈 初始化实时数据流...")
            self.realtime_manager = RealtimeDataManager(
                data_provider=self.ib_adapter.data_provider
            )

            # 4. 初始化执行管理器
            logger.info("⚡ 初始化交易执行...")
            execution_config = self.config.get('execution', {})
            self.execution_manager = ExecutionManager(
                broker=self.ib_adapter.broker,
                config=execution_config
            )

            # 5. 初始化交易引擎
            logger.info("🎯 初始化交易引擎...")
            risk_config = self.config.get('risk', {})
            self.trading_engine = TradingEngine(
                broker=self.ib_adapter.broker,
                risk_config=risk_config
            )

            # 6. 建立组件间连接
            self._setup_component_connections()

            self.is_initialized = True
            self.start_time = datetime.now()

            logger.info("✅ 量化交易框架初始化完成")
            self._print_system_status()

        except Exception as e:
            logger.error(f"❌ 应用初始化失败: {e}")
            self.cleanup()
            raise

    def start(self):
        """启动应用 - 生产级启动流程"""
        if not self.is_initialized:
            raise RuntimeError("应用未初始化，请先调用initialize()")

        if self.is_running:
            logger.warning("应用已在运行")
            return

        try:
            logger.info("🎬 启动量化交易系统")

            # 1. 启动实时数据管理器
            logger.info("📡 启动实时数据流...")
            self.realtime_manager.start()

            # 2. 启动执行管理器
            logger.info("⚡ 启动交易执行引擎...")
            self.execution_manager.start()

            # 3. 启动交易引擎
            logger.info("🎯 启动策略交易引擎...")
            self.trading_engine.start()

            # 4. 验证所有组件状态
            self._verify_system_health()

            self.is_running = True
            logger.info("🚀 量化交易系统启动成功")

            # 5. 打印运行状态
            self._print_runtime_status()

        except Exception as e:
            logger.error(f"❌ 系统启动失败: {e}")
            self.stop()
            raise

    def stop(self):
        """停止应用 - 优雅关闭"""
        if not self.is_running:
            return

        try:
            logger.info("🛑 正在停止量化交易系统...")

            # 1. 停止交易引擎（防止新的交易信号）
            if self.trading_engine:
                logger.info("⏸️ 停止策略引擎...")
                self.trading_engine.stop()

            # 2. 停止实时数据流
            if self.realtime_manager:
                logger.info("📊 停止实时数据流...")
                self.realtime_manager.stop()

            # 3. 处理待处理订单
            if self.execution_manager:
                logger.info("💼 处理待处理订单...")
                self._handle_pending_orders()
                self.execution_manager.stop()

            # 4. 断开IB连接
            if self.ib_adapter:
                logger.info("🔌 断开IB连接...")
                self.ib_adapter.disconnect()

            # 5. 保存运行统计
            self._save_session_stats()

            self.is_running = False
            self._shutdown_event.set()
            logger.info("✅ 量化交易系统已安全停止")

        except Exception as e:
            logger.error(f"❌ 停止系统时出错: {e}")

    def cleanup(self):
        """清理资源"""
        self.stop()
        self.is_initialized = False
        logger.info("🧹 系统资源已清理")

    def add_strategy(self, strategy):
        """添加策略 - 增强版本"""
        if not self.trading_engine:
            raise RuntimeError("交易引擎未初始化")

        try:
            # 验证策略
            if not hasattr(strategy, 'name') or not strategy.name:
                raise ValueError("策略必须有有效的名称")

            # 添加到交易引擎
            self.trading_engine.add_strategy(strategy)

            # 如果实时管理器可用，为策略订阅数据
            if self.realtime_manager and hasattr(strategy, 'get_required_symbols'):
                symbols = strategy.get_required_symbols()
                for symbol in symbols:
                    self.realtime_manager.subscribe(symbol, strategy.on_market_data)

            logger.info(f"✅ 策略已添加: {strategy.name}")

        except Exception as e:
            logger.error(f"❌ 添加策略失败: {e}")
            raise

    def run_backtest(self, strategy_name: str, start_date: datetime, end_date: datetime):
        """运行回测 - 增强版本"""
        if not self.is_initialized:
            raise RuntimeError("应用未初始化")

        try:
            logger.info(f"📊 开始回测: {strategy_name} ({start_date.date()} → {end_date.date()})")

            # 获取历史数据
            symbol = self.config['strategy']['symbol']
            historical_data = self.historical_data_manager.get_data(symbol, start_date, end_date)

            if historical_data.empty:
                raise ValueError(f"无法获取 {symbol} 的历史数据")

            logger.info(f"✅ 历史数据加载完成: {len(historical_data)} 条记录")

            # 创建回测引擎
            backtest_config = self.config.get('backtest', {})
            backtest_engine = BacktestEngine(
                historical_data=historical_data,
                initial_capital=backtest_config.get('initial_capital', 100000),
                commission=backtest_config.get('commission', 0.005),
                slippage=backtest_config.get('slippage', 0.001)
            )

            # 创建策略实例
            strategy = self._create_strategy(strategy_name)
            if not strategy:
                raise ValueError(f"无法创建策略: {strategy_name}")

            backtest_engine.add_strategy(strategy)

            # 运行回测
            logger.info("🎯 执行回测计算...")
            result = backtest_engine.run()

            # 保存并展示结果
            self._save_backtest_results(strategy_name, result)
            self._print_backtest_summary(result)

            logger.info("✅ 回测完成")
            return result

        except Exception as e:
            logger.error(f"❌ 回测失败: {e}")
            raise

    def run_live_trading(self):
        """运行实时交易"""
        if not self.is_running:
            self.start()

        try:
            # 启动交互式监控
            self._run_interactive_monitoring()

        except KeyboardInterrupt:
            logger.info("\n⏹️ 收到中断信号")
        except Exception as e:
            logger.error(f"❌ 实时交易运行失败: {e}")
            raise
        finally:
            self.stop()

    def _run_interactive_monitoring(self):
        """运行交互式监控"""
        logger.info("\n💡 交易系统运行中... 可用命令:")
        logger.info("  'status' - 查看系统状态")
        logger.info("  'stats'  - 查看统计信息")
        logger.info("  'orders' - 查看订单状态")
        logger.info("  'positions' - 查看持仓")
        logger.info("  'quit'   - 退出系统")
        logger.info("  Ctrl+C   - 强制退出")
        logger.info("")

        while self.is_running and not self._shutdown_event.is_set():
            try:
                # 非阻塞输入检查
                if sys.stdin in select.select([sys.stdin], [], [], 0.1)[0]:
                    command = input("> ").strip().lower()
                    self._handle_command(command)

                # 定期状态检查
                time.sleep(1)

            except EOFError:
                break
            except Exception as e:
                logger.error(f"❌ 监控循环错误: {e}")

    def _handle_command(self, command: str):
        """处理交互式命令"""
        try:
            if command == 'quit':
                self.stop()
            elif command == 'status':
                self._print_current_status()
            elif command == 'stats':
                self._print_current_statistics()
            elif command == 'orders':
                self._print_order_status()
            elif command == 'positions':
                self._print_positions()
            elif command == 'help':
                logger.info("可用命令: status, stats, orders, positions, quit, help")
            elif command:
                logger.info(f"未知命令: {command}. 输入 'help' 查看帮助")

        except Exception as e:
            logger.error(f"❌ 命令处理错误: {e}")

    def get_system_status(self) -> Dict:
        """获取系统状态"""
        status = {
            'mode': self.mode,
            'initialized': self.is_initialized,
            'running': self.is_running,
            'uptime_seconds': 0,
            'ib_connected': False,
            'components': {}
        }

        if self.start_time:
            status['uptime_seconds'] = (datetime.now() - self.start_time).total_seconds()

        if self.ib_adapter:
            status['ib_connected'] = (
                self.ib_adapter.data_provider.connected and
                self.ib_adapter.broker.connected
            )

        # 组件状态
        if self.realtime_manager:
            status['components']['realtime_data'] = self.realtime_manager.get_statistics()

        if self.execution_manager:
            status['components']['execution'] = self.execution_manager.get_statistics()

        if self.trading_engine:
            status['components']['trading'] = self.trading_engine.get_portfolio_summary()

        return status

    def _setup_component_connections(self):
        """建立组件间连接"""
        # 连接实时数据到交易引擎
        if self.realtime_manager and self.trading_engine:
            self.realtime_manager.add_event_handler(self.trading_engine.process_event)

        # 连接执行管理器到交易引擎
        if self.execution_manager and self.trading_engine:
            self.execution_manager.add_event_handler(self.trading_engine.process_event)

        logger.info("🔗 组件连接已建立")

    def _verify_system_health(self):
        """验证系统健康状态"""
        checks = []

        # IB连接检查
        if self.ib_adapter and self.ib_adapter.data_provider.connected:
            checks.append(("IB数据连接", True))
        else:
            checks.append(("IB数据连接", False))

        if self.ib_adapter and self.ib_adapter.broker.connected:
            checks.append(("IB交易连接", True))
        else:
            checks.append(("IB交易连接", False))

        # 组件状态检查
        checks.append(("实时数据管理器", self.realtime_manager.is_running if self.realtime_manager else False))
        checks.append(("执行管理器", self.execution_manager.is_running if self.execution_manager else False))
        checks.append(("交易引擎", self.trading_engine.is_running if self.trading_engine else False))

        # 打印检查结果
        logger.info("🏥 系统健康检查:")
        for name, status in checks:
            status_icon = "✅" if status else "❌"
            logger.info(f"   {status_icon} {name}")

        # 如果有关键组件失败，抛出异常
        failed_critical = [name for name, status in checks[:3] if not status]  # 前3个是关键组件
        if failed_critical:
            raise RuntimeError(f"关键组件启动失败: {', '.join(failed_critical)}")

    def _print_system_status(self):
        """打印系统状态"""
        logger.info("""
🎯 量化交易框架状态:""")
        logger.info(f"   模式: {self.mode.upper()}")
        logger.info(f"   初始化: {'✅' if self.is_initialized else '❌'}")
        logger.info(f"   运行中: {'✅' if self.is_running else '❌'}")

        if self.ib_adapter:
            logger.info(f"   IB连接: {'✅' if self.ib_adapter.data_provider.connected else '❌'}")

    def _print_runtime_status(self):
        """打印运行时状态"""
        logger.info("""
🚀 系统已就绪:""")
        logger.info(f"   📡 实时数据: {'运行中' if self.realtime_manager.is_running else '未运行'}")
        logger.info(f"   ⚡ 交易执行: {'运行中' if self.execution_manager.is_running else '未运行'}")
        logger.info(f"   🎯 策略引擎: {'运行中' if self.trading_engine.is_running else '未运行'}")

    def _print_current_status(self):
        """打印当前状态"""
        status = self.get_system_status()
        logger.info("\n📊 当前系统状态:")
        logger.info(f"  运行模式: {status['mode'].upper()}")
        logger.info(f"  运行时长: {status['uptime_seconds']:.0f} 秒")
        logger.info(f"  IB连接: {'✅' if status['ib_connected'] else '❌'}")

        if 'realtime_data' in status['components']:
            rt_stats = status['components']['realtime_data']
            logger.info(f"  活跃订阅: {rt_stats['active_subscriptions']} 个")

        if 'execution' in status['components']:
            ex_stats = status['components']['execution']
            logger.info(f"  活跃订单: {ex_stats['active_orders']} 个")

    def _print_current_statistics(self):
        """打印当前统计"""
        logger.info("\n📈 系统统计:")

        if self.realtime_manager:
            stats = self.realtime_manager.get_statistics()
            logger.info(f"  数据更新: {stats['updates_processed']} 次")
            logger.info(f"  更新频率: {stats['update_rate']:.1f} 次/秒")

        if self.execution_manager:
            stats = self.execution_manager.get_statistics()
            logger.info(f"  订单总数: {stats['total_orders']}")
            logger.info(f"  成交率: {stats['fill_rate_percent']:.1f}%")

    def _print_order_status(self):
        """打印订单状态"""
        if not self.execution_manager:
            logger.info("执行管理器未初始化")
            return

        active_orders = self.execution_manager.get_active_orders()
        logger.info(f"\n📋 活跃订单 ({len(active_orders)} 个):")

        for execution in active_orders:
            order = execution.original_order
            logger.info(f"  {execution.order_id[:8]}... - {order.symbol} {order.side.value} {order.quantity} @ {execution.status.value}")

    def _print_positions(self):
        """打印持仓"""
        if not self.trading_engine:
            logger.info("交易引擎未初始化")
            return

        try:
            positions = self.ib_adapter.broker.get_positions()
            logger.info(f"\n💼 当前持仓 ({len(positions)} 个):")

            for pos in positions:
                pnl_indicator = "📈" if pos.total_pnl > 0 else "📉" if pos.total_pnl < 0 else "➡️"
                logger.info(f"  {pos.symbol}: {pos.quantity} @ ${pos.avg_cost:.2f} {pnl_indicator} ${pos.total_pnl:.2f}")

        except Exception as e:
            logger.error(f"获取持仓失败: {e}")

    def _handle_pending_orders(self):
        """处理待处理订单"""
        if not self.execution_manager:
            return

        active_orders = self.execution_manager.get_active_orders()
        if active_orders:
            logger.info(f"⏳ 处理 {len(active_orders)} 个待处理订单...")
            for execution in active_orders:
                try:
                    self.execution_manager.cancel_order(execution.order_id)
                except Exception as e:
                    logger.warning(f"取消订单失败 {execution.order_id}: {e}")

    def _save_session_stats(self):
        """保存会话统计"""
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
            self.stats['uptime_seconds'] = uptime
            logger.info(f"📊 会话统计: 运行时长 {uptime:.0f} 秒")

    def _create_strategy(self, strategy_name: str):
        """创建策略实例 - 需要在应用层导入具体策略"""
        logger.error(f"策略创建失败: {strategy_name}")
        logger.info("请在应用层导入并创建策略实例，例如:")
        logger.info("示例: from strategies.your_strategy import YourStrategy")
        logger.info("示例: strategy = YourStrategy('Your Strategy', config)")
        logger.info("app.add_strategy(strategy)")
        return None

    def _save_backtest_results(self, strategy_name: str, result):
        """保存回测结果"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # 将DataFrame转换为简单的数据结构以便JSON序列化
            equity_curve_dict = result.equity_curve.reset_index().to_dict('records')

            # 准备保存的数据
            save_data = {
                'strategy_name': strategy_name,
                'timestamp': timestamp,
                'metrics': {
                    'total_return': float(result.total_return),
                    'sharpe_ratio': float(result.sharpe_ratio),
                    'max_drawdown': float(result.max_drawdown),
                    'total_trades': int(result.total_trades)
                },
                'config': self.config,
                'equity_curve_records': len(equity_curve_dict),
                'trades_count': len(result.trades)
            }

            # 保存到文件
            results_dir = 'backtest_results'
            os.makedirs(results_dir, exist_ok=True)

            filename = f"{results_dir}/{strategy_name}_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"💾 回测结果已保存: {filename}")

        except Exception as e:
            logger.error(f"❌ 保存回测结果失败: {e}")

    def _print_backtest_summary(self, result):
        """打印回测摘要"""
        logger.info("\n📊 回测结果摘要:")
        logger.info(f"   📈 总收益率: {result.total_return:.2%}")
        logger.info(f"   📊 夏普比率: {result.sharpe_ratio:.2f}")
        logger.info(f"   📉 最大回撤: {result.max_drawdown:.2%}")
        logger.info(f"   🔄 总交易次数: {result.total_trades}")


def create_production_config(mode: str = 'paper') -> Dict:
    """创建生产级配置"""
    return {
        'mode': mode,
        'ib': {
            'host': '127.0.0.1',
            'port': 7497 if mode == 'paper' else 7496,
            'client_id': 1,
            'timeout': 10
        },
        'strategy': {
            'symbol': 'SPY',  # 示例股票代码
            'exchange': 'SMART',
            'currency': 'USD'
        },
        'risk': {
            'max_position_size': 1000,
            'max_portfolio_risk': 0.02,
            'max_single_position_risk': 0.01,
            'max_drawdown': 0.1,
            'stop_loss_pct': 0.05
        },
        'execution': {
            'min_order_size': 1,
            'max_order_size': 10000,
            'max_order_value': 100000,
            'order_timeout': 300
        },
        'backtest': {
            'initial_capital': 100000,
            'commission': 0.005,
            'slippage': 0.001
        }
    }


def main():
    """主函数 - 生产级启动流程"""
    app = None

    try:
        # 解析命令行参数
        if len(sys.argv) < 2:
            print("🚀 生产级量化交易框架")
            print("用法: python production_trading_app.py [live|backtest|paper] [options]")
            print("\n模式说明:")
            print("  paper    - 模拟交易 (推荐新手)")
            print("  live     - 实盘交易 (需谨慎)")
            print("  backtest - 历史回测")
            print("\n示例:")
            print("  python production_trading_app.py paper    # 启动模拟交易")
            print("  python production_trading_app.py live     # 启动实盘交易")
            print("  python production_trading_app.py backtest # 运行历史回测")
            return

        mode = sys.argv[1].lower()
        if mode not in ['live', 'backtest', 'paper']:
            print(f"❌ 错误: 无效模式 '{mode}'")
            print("支持的模式: live, backtest, paper")
            return

        # 创建应用配置
        logger.info(f"🔧 加载 {mode.upper()} 模式配置...")
        app_config = create_production_config(mode)

        # 创建应用实例
        app = ProductionTradingApplication(app_config)

        if mode == 'backtest':
            # 回测模式
            logger.info("📊 启动回测模式")
            app.initialize()

            # 设置回测参数
            from datetime import datetime
            start_date = datetime(2023, 1, 1)
            end_date = datetime(2023, 12, 31)

            # result = app.run_backtest('strategy_name', start_date, end_date)
            logger.info("回测需要配置具体策略")

        else:
            # 实时交易模式
            if mode == 'live':
                # 实盘交易确认
                print("⚠️ 您即将启动实盘交易模式")
                confirmation = input("请输入 'YES' 确认继续: ")
                if confirmation != 'YES':
                    print("已取消")
                    return

                logger.warning("🔴 实盘交易模式已启动 - 请谨慎操作")
            else:
                logger.info("🟢 模拟交易模式已启动")

            # 初始化应用
            app.initialize()

            # 添加策略 (示例 - 需要在应用层导入策略)
            # from strategies.your_strategy import YourStrategy, YourConfig
            # config = YourConfig(app_config.get('strategy', {}))
            # strategy = YourStrategy('Your Strategy', config.to_dict())
            # app.add_strategy(strategy)
            logger.info("请在应用层导入并添加策略 - 见日志中的示例代码")

            # 启动应用
            app.run_live_trading()

    except KeyboardInterrupt:
        logger.info("\n⏹️ 收到中断信号")
    except Exception as e:
        logger.error(f"❌ 应用运行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if app:
            app.cleanup()


if __name__ == "__main__":
    # 添加 select 模块导入（用于非阻塞输入）
    try:
        import select
    except ImportError:
        # Windows 系统的替代方案
        import msvcrt
        def select_replacement():
            return msvcrt.kbhit()
        select = type('select', (), {'select': lambda r,w,x,t: ([sys.stdin] if select_replacement() else [], [], [])})()

    main()