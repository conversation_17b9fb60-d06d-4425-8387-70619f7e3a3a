"""
量化交易应用主程序
"""
import logging
import time
import threading
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from pathlib import Path

from ..core.base import Event, EventType
from ..data.data_manager import HistoricalDataManager, RealtimeDataManager, DataStore
from ..engine.trading_engine import TradingEngine
from ..backtest.backtest_engine import BacktestEngine, BacktestConfig, PerformanceAnalyzer
from ..adapters.ib_adapter import IBFrameworkAdapter
# 策略导入已移至独立目录 strategies/
# 策略导入示例: from strategies.your_strategy import YourStrategy


logger = logging.getLogger(__name__)


class TradingApplication:
    """交易应用主类"""

    def __init__(self, config: Dict):
        self.config = config
        self.mode = config.get('mode', 'live')  # 'live', 'backtest', 'paper'

        # 初始化组件
        self.ib_adapter = None
        self.data_store = None
        self.historical_data_manager = None
        self.realtime_data_manager = None
        self.trading_engine = None

        # 策略管理
        self.strategies = {}

        # 运行控制
        self.is_running = False
        self.main_thread = None

        # 设置日志
        self._setup_logging()

    def _setup_logging(self):
        """设置日志"""
        log_level = self.config.get('log_level', 'INFO')
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

        # 创建logs目录
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)

        # 配置日志
        logging.basicConfig(
            level=getattr(logging, log_level),
            format=log_format,
            handlers=[
                logging.FileHandler(log_dir / 'trading_app.log'),
                logging.StreamHandler()
            ]
        )

    def initialize(self):
        """初始化应用组件"""
        logger.info(f"初始化交易应用 - 模式: {self.mode}")

        try:
            # 初始化数据组件
            self.data_store = DataStore("data/market_data.db")

            # 初始化IB适配器和数据组件
            self.ib_adapter = IBFrameworkAdapter()
            self.historical_data_manager = HistoricalDataManager(
                self.ib_adapter.data_provider, self.data_store
            )

            if self.mode in ['live', 'paper']:
                # 实时模式需要实时数据和交易引擎
                self.realtime_data_manager = RealtimeDataManager(
                    self.ib_adapter.data_provider
                )

                # 初始化交易引擎
                risk_config = self.config.get('risk', {})
                self.trading_engine = TradingEngine(self.ib_adapter.broker, risk_config)

                # 连接到IB
                if not self.ib_adapter.connect():
                    raise ConnectionError("无法连接到IB Gateway")

            elif self.mode == 'backtest':
                # 回测模式需要连接获取历史数据，但不需要实时数据和交易引擎
                if not self.ib_adapter.connect():
                    logger.warning("无法连接到IB Gateway，将使用本地缓存数据进行回测")

            # 加载策略
            self._load_strategies()

            logger.info("应用初始化完成")

        except Exception as e:
            logger.error(f"应用初始化失败: {e}")
            raise

    def _load_strategies(self):
        """加载策略"""
        strategies_config = self.config.get('strategies', [])

        for strategy_config in strategies_config:
            strategy_type = strategy_config.get('type')
            strategy_name = strategy_config.get('name', strategy_type)

            # 策略加载已移至应用层处理
            # 使用 add_strategy() 方法动态添加策略
            logger.info(f"策略配置: {strategy_name} ({strategy_type}) - 请使用add_strategy()方法添加具体策略实例")

    def run_backtest(self, strategy_name: str, start_date: datetime, end_date: datetime):
        """运行回测"""
        logger.info(f"开始回测策略: {strategy_name}")

        try:
            strategy = self.strategies.get(strategy_name)
            if not strategy:
                raise ValueError(f"策略不存在: {strategy_name}")

            # 获取历史数据
            symbol = strategy.symbol
            data = self.historical_data_manager.get_data(symbol, start_date, end_date)

            if data.empty:
                raise ValueError(f"无法获取历史数据: {symbol}")

            # 配置回测
            backtest_config = BacktestConfig(
                initial_capital=self.config.get('initial_capital', 100000),
                commission_per_share=self.config.get('commission', 0.001),
                slippage_pct=self.config.get('slippage', 0.0001),
                start_date=start_date,
                end_date=end_date
            )

            # 运行回测
            backtest_engine = BacktestEngine(backtest_config)
            result = backtest_engine.run(strategy, data)

            # 分析结果
            metrics = PerformanceAnalyzer.calculate_metrics(result)
            report = PerformanceAnalyzer.generate_report(result, metrics)

            print(report)

            # 保存结果
            self._save_backtest_results(strategy_name, result, metrics)

            return result, metrics

        except Exception as e:
            logger.error(f"回测失败: {e}")
            raise

    def run_live_trading(self):
        """运行实时交易"""
        logger.info("开始实时交易")

        try:
            if not self.trading_engine:
                raise RuntimeError("交易引擎未初始化")

            # 启动组件
            self.realtime_data_manager.start()
            self.trading_engine.start()

            # 订阅数据
            for strategy in self.strategies.values():
                self.realtime_data_manager.subscribe(
                    strategy.symbol,
                    self._on_market_data
                )

            self.is_running = True

            # 主循环
            self._run_main_loop()

        except Exception as e:
            logger.error(f"实时交易运行失败: {e}")
            raise
        finally:
            self._cleanup()

    def _on_market_data(self, market_data):
        """处理实时市场数据"""
        try:
            # 创建市场数据事件
            event = Event(EventType.MARKET_DATA, market_data.timestamp, market_data)

            # 传递给交易引擎
            self.trading_engine.process_event(event)

        except Exception as e:
            logger.error(f"处理市场数据失败: {e}")

    def _run_main_loop(self):
        """主循环"""
        logger.info("进入主循环")

        while self.is_running:
            try:
                # 定期检查和报告
                time.sleep(60)  # 每分钟检查一次

                # 获取组合状态
                portfolio_summary = self.trading_engine.get_portfolio_summary()
                logger.info(f"组合状态: 总市值=${portfolio_summary['total_market_value']:.2f}, "
                           f"总盈亏=${portfolio_summary['total_pnl']:.2f}")

                # 检查策略状态
                for strategy_name, strategy in self.strategies.items():
                    if hasattr(strategy, 'get_strategy_state'):
                        state = strategy.get_strategy_state()
                        logger.debug(f"策略 {strategy_name} 状态: {state['state']}")

            except KeyboardInterrupt:
                logger.info("接收到停止信号")
                self.stop()
            except Exception as e:
                logger.error(f"主循环异常: {e}")
                time.sleep(5)  # 等待后继续

    def _save_backtest_results(self, strategy_name: str, result, metrics: Dict):
        """保存回测结果"""
        try:
            results_dir = Path('results')
            results_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = results_dir / f"{strategy_name}_backtest_{timestamp}.json"

            # 准备保存数据，处理DataFrame序列化
            equity_curve_dict = result.equity_curve.reset_index().to_dict('records')

            save_data = {
                'strategy_name': strategy_name,
                'timestamp': timestamp,
                'metrics': metrics,
                'config': self.config,
                'equity_curve_records': len(equity_curve_dict),
                'trades_count': len(result.trades),
                'summary': {
                    'total_return': metrics.get('Total Return', 0),
                    'sharpe_ratio': metrics.get('Sharpe Ratio', 0),
                    'max_drawdown': metrics.get('Max Drawdown', 0)
                }
            }

            import json
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)

            logger.info(f"回测结果已保存: {filename}")

        except Exception as e:
            logger.error(f"保存回测结果失败: {e}")

    def stop(self):
        """停止应用"""
        logger.info("正在停止交易应用...")

        self.is_running = False

        if self.trading_engine:
            self.trading_engine.stop()

        if self.realtime_data_manager:
            self.realtime_data_manager.stop()

        self._cleanup()

    def _cleanup(self):
        """清理资源"""
        try:
            if self.ib_adapter:
                self.ib_adapter.disconnect()
            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {e}")

    def get_status(self) -> Dict:
        """获取应用状态"""
        status = {
            'mode': self.mode,
            'is_running': self.is_running,
            'strategies': list(self.strategies.keys()),
            'connected': bool(self.ib_adapter and self.ib_adapter.data_provider.connected)
        }

        if self.trading_engine:
            portfolio = self.trading_engine.get_portfolio_summary()
            status.update({
                'portfolio_value': portfolio['total_market_value'],
                'total_pnl': portfolio['total_pnl'],
                'positions_count': portfolio['total_positions']
            })

        return status


def create_app_config(mode: str = 'live') -> Dict:
    """创建应用配置"""
    return {
        'mode': mode,
        'log_level': 'INFO',
        'initial_capital': 100000,
        'commission': 0.001,
        'slippage': 0.0001,
        'risk': {
            'max_position_size': 1000,
            'max_portfolio_risk': 0.2,
            'max_single_position_risk': 0.1,
            'stop_loss_pct': 0.05
        },
        'strategies': [
            # 示例策略配置 - 需要根据实际策略调整
            # {
            #     'type': 'custom_strategy',
            #     'name': 'strategy_main',
            #     'params': {
            #         'symbol': 'SPY',
            #         'param1': 'value1',
            #         'param2': 'value2'
            #     }
            # }
        ]
    }


def main():
    """主函数"""
    import sys

    if len(sys.argv) < 2:
        print("用法: python trading_app.py [live|backtest|paper]")
        sys.exit(1)

    mode = sys.argv[1].lower()
    if mode not in ['live', 'backtest', 'paper']:
        print("模式必须是: live, backtest, 或 paper")
        sys.exit(1)

    # 创建应用
    config = create_app_config(mode)
    app = TradingApplication(config)

    try:
        app.initialize()

        if mode == 'backtest':
            # 回测模式
            start_date = datetime.now() - timedelta(days=365)
            end_date = datetime.now()
            # 示例：app.run_backtest('strategy_name', start_date, end_date)
            logger.info("回测模式需要配置具体的策略")
        else:
            # 实时交易模式
            app.run_live_trading()

    except KeyboardInterrupt:
        print("\n接收到停止信号")
    except Exception as e:
        print(f"应用运行失败: {e}")
        logger.error(f"应用运行失败: {e}")
    finally:
        app.stop()


if __name__ == "__main__":
    main()