"""
企业级日志监控系统
实现结构化日志、实时监控、异常警报
"""
import logging
import logging.handlers
import json
import sys
import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from pathlib import Path
import threading
from collections import deque, defaultdict


@dataclass
class LogEvent:
    """日志事件结构"""
    timestamp: str
    level: str
    logger_name: str
    message: str
    module: str
    function: str
    line_number: int
    thread_id: int
    process_id: int
    extra_data: Dict[str, Any]


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def __init__(self, include_extra: bool = True):
        super().__init__()
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        # 基础日志信息
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created, tz=timezone.utc).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread_id': record.thread,
            'process_id': record.process
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # 添加额外数据
        if self.include_extra:
            extra_data = {}
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                              'filename', 'module', 'lineno', 'funcName', 'created', 
                              'msecs', 'relativeCreated', 'thread', 'threadName', 
                              'processName', 'process', 'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                    try:
                        # 确保值可以JSON序列化
                        json.dumps(value)
                        extra_data[key] = value
                    except (TypeError, ValueError):
                        extra_data[key] = str(value)
            
            if extra_data:
                log_data['extra'] = extra_data
        
        return json.dumps(log_data, ensure_ascii=False)


class TradingLogFilter(logging.Filter):
    """交易日志过滤器"""
    
    def __init__(self, component: Optional[str] = None):
        super().__init__()
        self.component = component
    
    def filter(self, record: logging.LogRecord) -> bool:
        """过滤日志记录"""
        # 添加组件标识
        if self.component:
            record.component = self.component
        
        # 添加交易相关标识
        if hasattr(record, 'order_id'):
            record.trading_event = True
        
        return True


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, window_size: int = 1000):
        self.window_size = window_size
        self.log_counts = defaultdict(lambda: deque(maxlen=window_size))
        self.error_counts = defaultdict(lambda: deque(maxlen=window_size))
        self.response_times = deque(maxlen=window_size)
        self._lock = threading.Lock()
    
    def record_log(self, level: str, logger_name: str):
        """记录日志事件"""
        with self._lock:
            timestamp = datetime.now(timezone.utc)
            self.log_counts[level].append(timestamp)
            self.log_counts[f"{logger_name}_{level}"].append(timestamp)
    
    def record_error(self, error_type: str, component: str):
        """记录错误事件"""
        with self._lock:
            timestamp = datetime.now(timezone.utc)
            self.error_counts[error_type].append(timestamp)
            self.error_counts[f"{component}_{error_type}"].append(timestamp)
    
    def record_response_time(self, duration: float):
        """记录响应时间"""
        with self._lock:
            self.response_times.append({
                'timestamp': datetime.now(timezone.utc),
                'duration': duration
            })
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取指标统计"""
        with self._lock:
            now = datetime.now(timezone.utc)
            
            # 计算各级别日志数量
            log_stats = {}
            for key, events in self.log_counts.items():
                # 最近1小时的事件
                recent_events = [e for e in events if (now - e).total_seconds() < 3600]
                log_stats[f"{key}_last_hour"] = len(recent_events)
                log_stats[f"{key}_total"] = len(events)
            
            # 计算错误统计
            error_stats = {}
            for key, events in self.error_counts.items():
                recent_events = [e for e in events if (now - e).total_seconds() < 3600]
                error_stats[f"{key}_last_hour"] = len(recent_events)
                error_stats[f"{key}_total"] = len(events)
            
            # 计算响应时间统计
            recent_response_times = [
                rt['duration'] for rt in self.response_times 
                if (now - rt['timestamp']).total_seconds() < 3600
            ]
            
            response_stats = {}
            if recent_response_times:
                response_stats = {
                    'avg_response_time': sum(recent_response_times) / len(recent_response_times),
                    'max_response_time': max(recent_response_times),
                    'min_response_time': min(recent_response_times),
                    'response_count': len(recent_response_times)
                }
            
            return {
                'log_statistics': log_stats,
                'error_statistics': error_stats,
                'response_statistics': response_stats,
                'collection_time': now.isoformat()
            }


class AlertManager:
    """警报管理器"""
    
    def __init__(self):
        self.alert_rules: List[Dict[str, Any]] = []
        self.active_alerts: Dict[str, Dict[str, Any]] = {}
        self.alert_handlers: List[callable] = []
        self._lock = threading.Lock()
    
    def add_alert_rule(self, rule_id: str, condition: str, threshold: float, 
                      message: str, severity: str = "warning"):
        """添加警报规则"""
        rule = {
            'id': rule_id,
            'condition': condition,
            'threshold': threshold,
            'message': message,
            'severity': severity,
            'enabled': True
        }
        
        with self._lock:
            self.alert_rules.append(rule)
    
    def add_alert_handler(self, handler: callable):
        """添加警报处理器"""
        self.alert_handlers.append(handler)
    
    def check_alerts(self, metrics: Dict[str, Any]):
        """检查警报条件"""
        with self._lock:
            for rule in self.alert_rules:
                if not rule['enabled']:
                    continue
                
                try:
                    # 简单的条件检查逻辑
                    condition = rule['condition']
                    threshold = rule['threshold']
                    
                    # 从指标中获取值
                    value = self._extract_metric_value(metrics, condition)
                    
                    if value is not None and value > threshold:
                        self._trigger_alert(rule, value)
                    elif rule['id'] in self.active_alerts:
                        self._resolve_alert(rule['id'])
                
                except Exception as e:
                    logging.error(f"警报检查错误 {rule['id']}: {e}")
    
    def _extract_metric_value(self, metrics: Dict[str, Any], condition: str) -> Optional[float]:
        """从指标中提取值"""
        # 简单的路径解析，实际应用中可以使用更复杂的表达式解析
        parts = condition.split('.')
        value = metrics
        
        try:
            for part in parts:
                value = value[part]
            return float(value) if value is not None else None
        except (KeyError, TypeError, ValueError):
            return None
    
    def _trigger_alert(self, rule: Dict[str, Any], value: float):
        """触发警报"""
        alert_id = rule['id']
        
        if alert_id not in self.active_alerts:
            alert = {
                'id': alert_id,
                'rule': rule,
                'triggered_at': datetime.now(timezone.utc),
                'current_value': value,
                'message': rule['message'].format(value=value, threshold=rule['threshold'])
            }
            
            self.active_alerts[alert_id] = alert
            
            # 调用警报处理器
            for handler in self.alert_handlers:
                try:
                    handler(alert)
                except Exception as e:
                    logging.error(f"警报处理器错误: {e}")
    
    def _resolve_alert(self, alert_id: str):
        """解决警报"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts.pop(alert_id)
            alert['resolved_at'] = datetime.now(timezone.utc)
            
            # 通知警报已解决
            for handler in self.alert_handlers:
                try:
                    handler(alert, resolved=True)
                except Exception as e:
                    logging.error(f"警报解决处理器错误: {e}")


class TradingLoggerConfig:
    """交易日志配置管理器"""
    
    def __init__(self, log_dir: str = "logs", log_level: str = "INFO"):
        self.log_dir = Path(log_dir)
        self.log_level = getattr(logging, log_level.upper())
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager()
        
        # 创建日志目录
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置默认警报规则
        self._setup_default_alerts()
    
    def setup_logging(self):
        """设置日志系统"""
        # 根日志器配置
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器 - 结构化日志
        structured_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "trading_structured.log",
            maxBytes=100*1024*1024,  # 100MB
            backupCount=10
        )
        structured_handler.setLevel(logging.DEBUG)
        structured_handler.setFormatter(StructuredFormatter())
        root_logger.addHandler(structured_handler)
        
        # 错误日志处理器
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "trading_errors.log",
            maxBytes=50*1024*1024,  # 50MB
            backupCount=5
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(StructuredFormatter())
        root_logger.addHandler(error_handler)
        
        # 交易事件日志处理器
        trading_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "trading_events.log",
            maxBytes=200*1024*1024,  # 200MB
            backupCount=20
        )
        trading_handler.setLevel(logging.INFO)
        trading_handler.addFilter(lambda record: hasattr(record, 'trading_event'))
        trading_handler.setFormatter(StructuredFormatter())
        root_logger.addHandler(trading_handler)
        
        # 添加指标收集处理器
        metrics_handler = MetricsHandler(self.metrics_collector)
        metrics_handler.setLevel(logging.DEBUG)
        root_logger.addHandler(metrics_handler)
        
        logging.info("📊 企业级日志系统初始化完成")
    
    def _setup_default_alerts(self):
        """设置默认警报规则"""
        # 错误率警报
        self.alert_manager.add_alert_rule(
            rule_id="high_error_rate",
            condition="error_statistics.ERROR_last_hour",
            threshold=10,
            message="错误率过高: {value} 错误/小时 (阈值: {threshold})",
            severity="warning"
        )
        
        # 响应时间警报
        self.alert_manager.add_alert_rule(
            rule_id="slow_response",
            condition="response_statistics.avg_response_time",
            threshold=5.0,
            message="平均响应时间过慢: {value:.2f}秒 (阈值: {threshold}秒)",
            severity="warning"
        )
    
    def get_logger(self, name: str, component: Optional[str] = None) -> logging.Logger:
        """获取配置好的日志器"""
        logger = logging.getLogger(name)
        
        if component:
            logger.addFilter(TradingLogFilter(component))
        
        return logger
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取监控指标"""
        metrics = self.metrics_collector.get_metrics()
        
        # 检查警报
        self.alert_manager.check_alerts(metrics)
        
        # 添加警报信息
        metrics['active_alerts'] = list(self.alert_manager.active_alerts.values())
        
        return metrics


class MetricsHandler(logging.Handler):
    """指标收集处理器"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        super().__init__()
        self.metrics_collector = metrics_collector
    
    def emit(self, record: logging.LogRecord):
        """处理日志记录"""
        try:
            # 记录日志级别统计
            self.metrics_collector.record_log(record.levelname, record.name)
            
            # 记录错误统计
            if record.levelno >= logging.ERROR:
                component = getattr(record, 'component', 'unknown')
                self.metrics_collector.record_error('ERROR', component)
            
            # 记录响应时间（如果有）
            if hasattr(record, 'response_time'):
                self.metrics_collector.record_response_time(record.response_time)
        
        except Exception:
            # 避免日志处理器本身出错影响主程序
            pass


# 全局日志配置实例
logger_config = TradingLoggerConfig()


def setup_trading_logging(log_dir: str = "logs", log_level: str = "INFO"):
    """设置交易日志系统"""
    global logger_config
    logger_config = TradingLoggerConfig(log_dir, log_level)
    logger_config.setup_logging()
    return logger_config


def get_trading_logger(name: str, component: Optional[str] = None) -> logging.Logger:
    """获取交易日志器"""
    return logger_config.get_logger(name, component)


def get_monitoring_metrics() -> Dict[str, Any]:
    """获取监控指标"""
    return logger_config.get_metrics()


def add_alert_handler(handler: callable):
    """添加警报处理器"""
    logger_config.alert_manager.add_alert_handler(handler)
