"""
企业级交易执行系统
整合所有组件，提供统一的交易执行接口
"""
import logging
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timezone
from dataclasses import dataclass

from ..core.base import Order, OrderSide, OrderType, TimeInForce, Position
from ..gateway.ib_gateway import IBGateway, ConnectionConfig, RateLimitConfig
from ..oms.order_manager import OrderManager
from ..risk.risk_manager import RiskManager
from ..portfolio.portfolio_manager import PortfolioManager
from ..monitoring.logger_config import get_trading_logger, get_monitoring_metrics

logger = get_trading_logger(__name__, "TradingSystem")


@dataclass
class TradingSystemConfig:
    """交易系统配置"""
    # IB连接配置
    ib_host: str = "127.0.0.1"
    ib_port: int = 7497
    ib_client_id: int = 1
    
    # 风险管理配置
    max_position_size: float = 10000.0
    max_daily_loss: float = 50000.0
    max_order_size: float = 1000.0
    
    # 系统配置
    enable_risk_management: bool = True
    enable_monitoring: bool = True
    log_level: str = "INFO"


class TradingSystem:
    """企业级交易执行系统"""
    
    def __init__(self, config: TradingSystemConfig):
        self.config = config
        self.is_running = False
        self._shutdown_event = threading.Event()
        
        # 初始化组件
        self._initialize_components()
        
        # 设置事件处理
        self._setup_event_handlers()
        
        logger.info("🚀 企业级交易执行系统初始化完成")
    
    def _initialize_components(self):
        """初始化系统组件"""
        # 1. 交易网关
        connection_config = ConnectionConfig(
            host=self.config.ib_host,
            port=self.config.ib_port,
            client_id=self.config.ib_client_id
        )
        
        rate_limit_config = RateLimitConfig(
            max_requests_per_second=50,
            max_orders_per_second=10,
            max_market_data_requests=100
        )
        
        self.gateway = IBGateway(connection_config, rate_limit_config)
        
        # 2. 订单管理系统
        self.order_manager = OrderManager()
        self.order_manager.add_gateway("ib", self.gateway)
        
        # 3. 风险管理系统
        self.risk_manager = RiskManager()
        
        # 4. 投资组合管理系统
        self.portfolio_manager = PortfolioManager()
        
        logger.info("✅ 所有系统组件初始化完成")
    
    def _setup_event_handlers(self):
        """设置事件处理器"""
        # 订单执行事件
        self.order_manager.add_event_handler('order_execution', self._on_order_execution)
        
        # 持仓更新事件
        self.portfolio_manager.add_event_handler('position_updated', self._on_position_updated)
        
        # 账户更新事件
        self.portfolio_manager.add_event_handler('account_updated', self._on_account_updated)
        
        logger.info("📡 事件处理器设置完成")
    
    def start(self) -> bool:
        """启动交易系统"""
        if self.is_running:
            logger.warning("⚠️ 交易系统已在运行")
            return True
        
        try:
            # 连接到交易网关
            if not self.gateway.connect():
                logger.error("❌ 无法连接到交易网关")
                return False
            
            self.is_running = True
            self._shutdown_event.clear()
            
            # 启动监控线程
            if self.config.enable_monitoring:
                self._start_monitoring_thread()
            
            logger.info("🟢 交易系统启动成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 交易系统启动失败: {e}")
            return False
    
    def stop(self):
        """停止交易系统"""
        if not self.is_running:
            logger.warning("⚠️ 交易系统未在运行")
            return
        
        logger.info("🔴 正在停止交易系统...")
        
        # 设置停止标志
        self.is_running = False
        self._shutdown_event.set()
        
        # 取消所有活跃订单
        active_orders = self.order_manager.get_active_orders()
        for order in active_orders:
            try:
                self.order_manager.cancel_order(order.order_id)
                logger.info(f"🚫 取消订单: {order.order_id}")
            except Exception as e:
                logger.error(f"取消订单失败 {order.order_id}: {e}")
        
        # 断开网关连接
        try:
            self.gateway.disconnect()
            logger.info("🔌 网关连接已断开")
        except Exception as e:
            logger.error(f"断开网关连接失败: {e}")
        
        logger.info("🔴 交易系统已停止")
    
    def submit_order(self, symbol: str, side: OrderSide, quantity: float,
                    order_type: OrderType = OrderType.MARKET,
                    price: Optional[float] = None,
                    stop_price: Optional[float] = None,
                    time_in_force: TimeInForce = TimeInForce.DAY) -> Optional[str]:
        """提交订单"""
        if not self.is_running:
            logger.error("❌ 交易系统未运行，无法提交订单")
            return None
        
        try:
            # 创建订单
            order = self.order_manager.create_order(
                symbol=symbol,
                side=side,
                quantity=quantity,
                order_type=order_type,
                price=price,
                stop_price=stop_price,
                time_in_force=time_in_force
            )
            
            # 风险检查
            if self.config.enable_risk_management:
                context = self._build_risk_context()
                approved, messages = self.risk_manager.check_pre_trade_risk(order, context)
                
                if not approved:
                    logger.warning(f"🚫 订单被风控拒绝: {order.order_id}")
                    for msg in messages:
                        logger.warning(f"   - {msg}")
                    return None
                
                if messages:  # 有警告信息
                    for msg in messages:
                        logger.warning(f"⚠️ 风险警告: {msg}")
            
            # 提交订单
            success = self.order_manager.submit_order(order.order_id)
            
            if success:
                logger.info(f"📋 订单提交成功: {order.order_id} {symbol} {side.value} {quantity}")
                return order.order_id
            else:
                logger.error(f"❌ 订单提交失败: {order.order_id}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 提交订单异常: {e}")
            return None
    
    def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        if not self.is_running:
            logger.error("❌ 交易系统未运行，无法取消订单")
            return False
        
        try:
            success = self.order_manager.cancel_order(order_id)
            if success:
                logger.info(f"🚫 订单取消成功: {order_id}")
            else:
                logger.error(f"❌ 订单取消失败: {order_id}")
            return success
            
        except Exception as e:
            logger.error(f"❌ 取消订单异常: {e}")
            return False
    
    def get_positions(self) -> List[Position]:
        """获取持仓"""
        try:
            return self.portfolio_manager.position_tracker.get_active_positions()
        except Exception as e:
            logger.error(f"❌ 获取持仓失败: {e}")
            return []
    
    def get_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """获取订单"""
        try:
            if symbol:
                return self.order_manager.get_orders_by_symbol(symbol)
            else:
                return list(self.order_manager.orders.values())
        except Exception as e:
            logger.error(f"❌ 获取订单失败: {e}")
            return []
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 基础状态
            status = {
                'system_running': self.is_running,
                'gateway_connected': self.gateway.connection_manager.state.value,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            # 订单管理统计
            status['order_management'] = self.order_manager.get_statistics()
            
            # 投资组合摘要
            status['portfolio'] = self.portfolio_manager.get_portfolio_summary().__dict__
            
            # 风险管理报告
            if self.config.enable_risk_management:
                status['risk_management'] = self.risk_manager.get_comprehensive_risk_report()
            
            # 网关统计
            status['gateway_statistics'] = self.gateway.get_statistics()
            
            # 监控指标
            if self.config.enable_monitoring:
                status['monitoring_metrics'] = get_monitoring_metrics()
            
            return status
            
        except Exception as e:
            logger.error(f"❌ 获取系统状态失败: {e}")
            return {'error': str(e)}
    
    def _build_risk_context(self) -> Dict[str, Any]:
        """构建风险检查上下文"""
        try:
            positions = self.portfolio_manager.position_tracker.get_active_positions()
            position_dict = {pos.symbol: pos.quantity for pos in positions}
            
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()
            
            return {
                'positions': position_dict,
                'daily_pnl': portfolio_summary.total_pnl,
                'total_market_value': portfolio_summary.total_market_value,
                'account_info': self.portfolio_manager.fund_manager.account_info.__dict__
            }
        except Exception as e:
            logger.error(f"构建风险上下文失败: {e}")
            return {}
    
    def _on_order_execution(self, data: Dict[str, Any]):
        """处理订单执行事件"""
        try:
            order = data['order']
            execution = data['execution']
            
            # 更新投资组合
            self.portfolio_manager.process_execution(execution, order)
            
            logger.info(f"💰 订单执行: {order.symbol} {execution.fill_quantity}@{execution.fill_price}",
                       extra={'trading_event': True, 'order_id': order.order_id})
            
        except Exception as e:
            logger.error(f"处理订单执行事件失败: {e}")
    
    def _on_position_updated(self, data: Dict[str, Any]):
        """处理持仓更新事件"""
        try:
            symbol = data['symbol']
            position = data['position']
            
            logger.info(f"📊 持仓更新: {symbol} {position.quantity}@{position.avg_cost:.4f}",
                       extra={'trading_event': True, 'symbol': symbol})
            
        except Exception as e:
            logger.error(f"处理持仓更新事件失败: {e}")
    
    def _on_account_updated(self, account_info):
        """处理账户更新事件"""
        try:
            logger.debug(f"💰 账户更新: 净值={account_info.net_liquidation_value:.2f}",
                        extra={'trading_event': True})
            
        except Exception as e:
            logger.error(f"处理账户更新事件失败: {e}")
    
    def _start_monitoring_thread(self):
        """启动监控线程"""
        def monitoring_worker():
            while not self._shutdown_event.wait(60):  # 每分钟检查一次
                try:
                    # 获取监控指标
                    metrics = get_monitoring_metrics()
                    
                    # 记录关键指标
                    if 'active_alerts' in metrics and metrics['active_alerts']:
                        logger.warning(f"🚨 活跃警报: {len(metrics['active_alerts'])} 个")
                    
                    # 检查系统健康状态
                    if not self.gateway.connection_manager.state.value == "connected":
                        logger.warning("⚠️ 网关连接异常")
                    
                except Exception as e:
                    logger.error(f"监控线程错误: {e}")
        
        monitoring_thread = threading.Thread(target=monitoring_worker, daemon=True)
        monitoring_thread.start()
        logger.info("📊 监控线程已启动")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()
