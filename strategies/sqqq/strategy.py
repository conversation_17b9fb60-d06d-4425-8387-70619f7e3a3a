"""
SQQQ反向ETF三引擎期权策略
基于trade框架的专业策略实现示例
"""
import logging
import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime

# 导入trade框架核心组件
from trade.core.base import Strategy, Signal, MarketData

logger = logging.getLogger(__name__)


class SQQQStrategy(Strategy):
    """
    SQQQ三引擎期权策略

    引擎一：建仓引擎 - RSI超卖时卖出Put期权收取权利金
    引擎二：成本湮灭 - RSI超买时卖出Covered Call降低成本
    引擎三：风险管理 - 动态止损和资金管理
    """

    def __init__(self, name: str = "SQQQ_Strategy", config: Dict[str, Any] = None):
        super().__init__(name)

        # 策略配置
        config = config or {}
        self.symbol = config.get('symbol', 'SQQQ')
        self.rsi_period = config.get('rsi_period', 14)
        self.sma_period = config.get('sma_period', 20)
        self.rsi_oversold = config.get('rsi_oversold', 30.0)
        self.rsi_overbought = config.get('rsi_overbought', 70.0)
        self.target_delta_put = config.get('target_delta_put', -0.3)
        self.target_delta_call = config.get('target_delta_call', 0.3)
        self.stop_loss_pct = config.get('stop_loss_pct', 0.10)

        # 策略状态
        self.current_state = "WAITING"  # WAITING, BUILDING, HOLDING, HEDGING
        self.position_cost = 0.0
        self.option_premiums_collected = 0.0
        self.total_trades = 0

        # 数据缓存
        self.price_history = []
        self.max_history_size = max(self.rsi_period, self.sma_period) + 20

        # 当前技术指标
        self.current_indicators = {}

        logger.info(f"SQQQ策略初始化完成: {self.name}")
        logger.info(f"配置: {self.symbol}, RSI({self.rsi_oversold}-{self.rsi_overbought}), SMA({self.sma_period})")

    def get_required_symbols(self) -> List[str]:
        """获取策略需要的股票代码"""
        return [self.symbol]

    def generate_signals(self, market_data: MarketData) -> List[Signal]:
        """生成交易信号"""
        signals = []

        try:
            # 更新价格历史
            self._update_price_history(market_data)

            # 计算技术指标
            if not self._calculate_indicators():
                return signals

            # 根据当前状态生成信号
            if self.current_state == "WAITING":
                # 检查建仓机会
                signal = self._check_entry_signal(market_data)
                if signal:
                    signals.append(signal)

            elif self.current_state == "HOLDING":
                # 检查对冲机会
                hedge_signal = self._check_hedge_signal(market_data)
                if hedge_signal:
                    signals.append(hedge_signal)

                # 检查止损
                stop_signal = self._check_stop_loss(market_data)
                if stop_signal:
                    signals.append(stop_signal)

        except Exception as e:
            logger.error(f"信号生成失败: {e}")

        return signals

    def on_market_data(self, data: MarketData) -> None:
        """处理市场数据"""
        try:
            # 更新指标
            self._update_price_history(data)
            self._calculate_indicators()

            # 记录关键信息
            if self.current_indicators:
                logger.debug(
                    f"SQQQ数据更新 - 价格: ${data.close:.2f}, "
                    f"RSI: {self.current_indicators.get('rsi', 0):.1f}, "
                    f"状态: {self.current_state}"
                )

        except Exception as e:
            logger.error(f"处理市场数据失败: {e}")

    def on_signal(self, signal: Signal) -> None:
        """处理交易信号"""
        logger.info(f"策略接收信号: {signal.signal_type} - {signal.reason}")

        # 更新策略状态
        if signal.signal_type in ['SELL_PUT', 'PUT_OPTION']:
            self.current_state = "BUILDING"
            self.total_trades += 1

        elif signal.signal_type in ['BUY', 'STOCK_ASSIGNMENT']:
            self.current_state = "HOLDING"
            self.position_cost = signal.metadata.get('price', 0)

        elif signal.signal_type in ['SELL_CALL', 'CALL_OPTION']:
            self.current_state = "HEDGING"

        elif signal.signal_type in ['SELL', 'CLOSE_POSITION']:
            self.current_state = "WAITING"
            self.position_cost = 0.0

    def _update_price_history(self, market_data: MarketData):
        """更新价格历史"""
        self.price_history.append({
            'timestamp': market_data.timestamp,
            'close': market_data.close,
            'high': market_data.high,
            'low': market_data.low,
            'volume': market_data.volume
        })

        # 维护历史数据大小
        if len(self.price_history) > self.max_history_size:
            self.price_history.pop(0)

    def _calculate_indicators(self) -> bool:
        """计算技术指标"""
        if len(self.price_history) < self.rsi_period:
            return False

        try:
            # 转换为DataFrame
            df = pd.DataFrame(self.price_history)
            prices = df['close']

            # 计算RSI
            rsi = self._calculate_rsi(prices, self.rsi_period)

            # 计算SMA
            sma = prices.rolling(window=self.sma_period).mean()

            # 计算EMA (指数移动平均)
            ema = prices.ewm(span=self.sma_period).mean()

            # 计算布林带
            bb_std = prices.rolling(window=20).std()
            bb_upper = sma.iloc[-1] + (bb_std.iloc[-1] * 2)
            bb_lower = sma.iloc[-1] - (bb_std.iloc[-1] * 2)

            self.current_indicators = {
                'rsi': rsi.iloc[-1] if not rsi.empty else 50,
                'sma': sma.iloc[-1] if not sma.empty else prices.iloc[-1],
                'ema': ema.iloc[-1] if not ema.empty else prices.iloc[-1],
                'bb_upper': bb_upper,
                'bb_lower': bb_lower,
                'price': prices.iloc[-1]
            }

            return True

        except Exception as e:
            logger.error(f"技术指标计算失败: {e}")
            return False

    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

        rs = gain / loss.replace(0, float('inf'))
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _check_entry_signal(self, market_data: MarketData) -> Optional[Signal]:
        """检查建仓信号 - 引擎一"""
        rsi = self.current_indicators.get('rsi')
        sma = self.current_indicators.get('sma')
        price = market_data.close

        # 建仓条件：RSI超卖 AND 价格低于SMA AND 价格接近布林带下轨
        if (rsi < self.rsi_oversold and
            price < sma and
            price <= self.current_indicators.get('bb_lower', price)):

            # 计算信号强度
            strength = min((self.rsi_oversold - rsi) / 20, 1.0)

            signal = Signal(
                symbol=self.symbol,
                signal_type='SELL_PUT',
                strength=strength,
                timestamp=market_data.timestamp,
                reason=f"引擎一激活: RSI({rsi:.1f})<{self.rsi_oversold}, 价格({price:.2f})<SMA({sma:.2f})",
                metadata={
                    'engine': 'building',
                    'rsi': rsi,
                    'sma': sma,
                    'price': price,
                    'target_delta': self.target_delta_put,
                    'option_type': 'PUT',
                    'strategy_state': self.current_state
                }
            )

            logger.info(f"🔥 生成建仓信号: {signal.reason}")
            return signal

        return None

    def _check_hedge_signal(self, market_data: MarketData) -> Optional[Signal]:
        """检查对冲信号 - 引擎二"""
        rsi = self.current_indicators.get('rsi')
        price = market_data.close

        # 对冲条件：RSI超买 AND 持有股票
        if rsi > self.rsi_overbought and self.position_cost > 0:

            strength = min((rsi - self.rsi_overbought) / 20, 1.0)

            signal = Signal(
                symbol=self.symbol,
                signal_type='SELL_CALL',
                strength=strength,
                timestamp=market_data.timestamp,
                reason=f"引擎二激活: RSI({rsi:.1f})>{self.rsi_overbought}, 成本湮灭机会",
                metadata={
                    'engine': 'cost_annihilation',
                    'rsi': rsi,
                    'price': price,
                    'position_cost': self.position_cost,
                    'target_delta': self.target_delta_call,
                    'option_type': 'CALL',
                    'strategy_state': self.current_state
                }
            )

            logger.info(f"⚡ 生成成本湮灭信号: {signal.reason}")
            return signal

        return None

    def _check_stop_loss(self, market_data: MarketData) -> Optional[Signal]:
        """检查止损信号 - 引擎三"""
        if self.position_cost <= 0:
            return None

        price = market_data.close
        loss_pct = (self.position_cost - price) / self.position_cost

        # 止损条件
        if loss_pct > self.stop_loss_pct:
            signal = Signal(
                symbol=self.symbol,
                signal_type='SELL',
                strength=1.0,
                timestamp=market_data.timestamp,
                reason=f"引擎三激活: 止损触发 亏损{loss_pct:.1%}>{self.stop_loss_pct:.1%}",
                metadata={
                    'engine': 'risk_management',
                    'loss_pct': loss_pct,
                    'price': price,
                    'position_cost': self.position_cost,
                    'strategy_state': self.current_state
                }
            )

            logger.warning(f"🛡️ 生成风控信号: {signal.reason}")
            return signal

        return None

    def get_strategy_state(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            'name': self.name,
            'symbol': self.symbol,
            'current_state': self.current_state,
            'position_cost': self.position_cost,
            'premiums_collected': self.option_premiums_collected,
            'total_trades': self.total_trades,
            'is_active': self.is_active,
            'indicators': self.current_indicators.copy(),
            'config': {
                'symbol': self.symbol,
                'rsi_period': self.rsi_period,
                'sma_period': self.sma_period,
                'rsi_oversold': self.rsi_oversold,
                'rsi_overbought': self.rsi_overbought,
                'target_delta_put': self.target_delta_put,
                'target_delta_call': self.target_delta_call,
                'stop_loss_pct': self.stop_loss_pct
            },
            'data_points': len(self.price_history)
        }

    def reset(self):
        """重置策略状态"""
        self.current_state = "WAITING"
        self.position_cost = 0.0
        self.option_premiums_collected = 0.0
        self.total_trades = 0
        self.price_history.clear()
        self.current_indicators.clear()

        logger.info(f"策略 {self.name} 状态已重置")

    def update_premium_collected(self, premium: float):
        """更新收取的权利金"""
        self.option_premiums_collected += premium
        logger.info(f"收取权利金: ${premium:.2f}, 累计: ${self.option_premiums_collected:.2f}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取策略绩效摘要"""
        current_price = self.current_indicators.get('price', 0)

        # 计算未实现盈亏
        unrealized_pnl = 0.0
        if self.position_cost > 0:
            unrealized_pnl = (current_price - self.position_cost) * 100  # 假设100股

        return {
            'total_trades': self.total_trades,
            'premiums_collected': self.option_premiums_collected,
            'position_cost': self.position_cost,
            'current_price': current_price,
            'unrealized_pnl': unrealized_pnl,
            'total_pnl': self.option_premiums_collected + unrealized_pnl,
            'strategy_state': self.current_state
        }