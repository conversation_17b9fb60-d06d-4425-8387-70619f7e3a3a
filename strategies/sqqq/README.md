# SQQQ反向ETF三引擎期权策略

## 概述

这是一个基于trade量化交易框架的SQQQ期权策略示例。SQQQ是ProShares UltraPro Short QQQ ETF，追踪纳斯达克100指数的三倍反向表现。

本策略采用**三引擎设计**，通过期权操作实现成本湮灭和风险管理。

## 策略原理

### 三引擎架构

#### 🔥 引擎一：建仓引擎
- **触发条件**: RSI < 30 AND 价格 < SMA(20)
- **操作**: 卖出Put期权收取权利金
- **目标**: 在市场超卖时以折扣价格建仓或收取权利金

#### ⚡ 引擎二：成本湮灭引擎
- **触发条件**: RSI > 70 AND 持有股票
- **操作**: 卖出Covered Call期权
- **目标**: 在市场超买时降低持仓成本或获得额外收益

#### 🛡️ 引擎三：风险管理引擎
- **触发条件**: 持续监控
- **操作**: 动态止损、仓位管理
- **目标**: 控制最大损失，保护资本

## 技术指标

- **RSI(14)**: 相对强弱指数，判断超买超卖
- **SMA(20)**: 简单移动平均线，判断趋势方向
- **EMA(12/26)**: 指数移动平均线
- **MACD**: 移动平均收敛散度
- **布林带**: 价格通道，判断价格位置

## 文件结构

```
strategies/sqqq/
├── __init__.py          # 模块初始化
├── strategy.py          # 核心策略实现
├── config.py           # 策略配置管理
├── analysis.py         # 市场分析工具
├── tools.py            # 独立工具脚本
└── README.md           # 本文档
```

## 快速开始

### 1. 获取实时数据
```bash
python strategies/sqqq/tools.py data --days 5
```

### 2. 分析期权数据
```bash
python strategies/sqqq/tools.py options --expiration 30
```

### 3. 完整策略分析
```bash
python strategies/sqqq/tools.py analysis --days 30
```

### 4. 在主框架中使用
```python
from strategies.sqqq import SQQQStrategy, SQQQConfig

# 创建策略配置
config = SQQQConfig({
    'rsi_oversold': 30,
    'rsi_overbought': 70,
    'stop_loss_pct': 0.10
})

# 创建策略实例
strategy = SQQQStrategy("my_sqqq_strategy", config.to_dict())

# 添加到交易应用
app.add_strategy(strategy)
```

## 配置选项

### 基础配置
- `symbol`: 交易标的 (默认: SQQQ)
- `exchange`: 交易所 (默认: SMART)
- `currency`: 货币 (默认: USD)

### 技术指标参数
- `rsi_period`: RSI周期 (默认: 14)
- `sma_period`: SMA周期 (默认: 20)
- `rsi_oversold`: RSI超卖阈值 (默认: 30)
- `rsi_overbought`: RSI超买阈值 (默认: 70)

### 期权策略参数
- `target_delta_put`: Put期权目标Delta (默认: -0.3)
- `target_delta_call`: Call期权目标Delta (默认: 0.3)
- `expiration_days`: 期权到期天数 (默认: 30)

### 风险管理参数
- `stop_loss_pct`: 止损百分比 (默认: 10%)
- `max_position_size`: 最大仓位 (默认: 1000)
- `position_size_pct`: 仓位比例 (默认: 10%)

## 预定义配置

### 保守配置
```python
from strategies.sqqq.config import CONSERVATIVE_CONFIG
config = SQQQConfig(CONSERVATIVE_CONFIG)
```

### 激进配置
```python
from strategies.sqqq.config import AGGRESSIVE_CONFIG
config = SQQQConfig(AGGRESSIVE_CONFIG)
```

### 平衡配置
```python
from strategies.sqqq.config import BALANCED_CONFIG
config = SQQQConfig(BALANCED_CONFIG)
```

## 策略监控

### 实时状态
```python
state = strategy.get_strategy_state()
print(f"当前状态: {state['current_state']}")
print(f"持仓成本: ${state['position_cost']:.2f}")
print(f"累计权利金: ${state['premiums_collected']:.2f}")
```

### 性能分析
```python
performance = strategy.get_performance_summary()
print(f"总交易次数: {performance['total_trades']}")
print(f"总盈亏: ${performance['total_pnl']:.2f}")
```

## 风险提示

1. **市场风险**: SQQQ是反向ETF，存在较高波动性
2. **期权风险**: 期权交易具有时间价值衰减风险
3. **流动性风险**: 某些期权合约可能流动性不足
4. **技术风险**: 策略基于技术指标，存在失效可能

## 注意事项

1. **数据权限**: 确保IB账户有SQQQ和期权的数据权限
2. **期权权限**: 确保账户具有期权交易权限
3. **资金管理**: 合理控制仓位大小
4. **模拟测试**: 建议先在模拟环境充分测试

## 扩展开发

### 添加新技术指标
```python
def _calculate_custom_indicator(self, data):
    # 自定义指标计算
    pass
```

### 修改信号生成逻辑
```python
def _check_custom_signal(self, market_data):
    # 自定义信号逻辑
    pass
```

### 扩展风险管理
```python
def _custom_risk_check(self, position):
    # 自定义风险检查
    pass
```

## 联系和支持

这是一个基于trade框架的策略示例，展示了如何构建专业的量化交易策略。

核心trade框架专注于：
- 🔗 稳定的broker连接
- 📊 可靠的数据获取
- ⚡ 专业的交易执行
- 🛡️ 完整的风险管理

策略层面可以根据需要灵活定制和扩展。