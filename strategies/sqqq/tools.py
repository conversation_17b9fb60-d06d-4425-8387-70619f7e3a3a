"""
SQQQ策略专用工具
提供数据获取、期权分析、策略监控等功能
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import logging
from datetime import datetime, timedelta
from typing import Dict, Any

from trade.app.production_trading_app import create_production_config, ProductionTradingApplication
from .analysis import SQQQAnalyzer
from .config import SQQQConfig

logger = logging.getLogger(__name__)


class SQQQTools:
    """SQQQ策略工具集"""

    def __init__(self, mode: str = 'paper'):
        self.mode = mode
        self.app = None
        self.analyzer = None

    def initialize(self):
        """初始化工具"""
        try:
            # 创建应用配置
            config = create_production_config(self.mode)

            # 创建应用实例
            self.app = ProductionTradingApplication(config)
            self.app.initialize()

            # 创建分析器
            self.analyzer = SQQQAnalyzer(self.app.ib_adapter.data_provider)

            logger.info("SQQQ工具初始化完成")
            return True

        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False

    def get_real_time_data(self, days: int = 5) -> Dict[str, Any]:
        """获取实时SQQQ数据和分析"""
        try:
            print("📊 正在获取SQQQ实时数据...")

            if not self.app:
                raise RuntimeError("工具未初始化")

            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            data = self.app.historical_data_manager.get_data('SQQQ', start_date, end_date)

            if data.empty:
                return {"error": "无法获取数据"}

            # 获取最新数据点
            latest = data.iloc[-1]
            latest_time = data.index[-1]

            # 进行市场分析
            analysis = self.analyzer.get_market_analysis(days)

            # 生成显示报告
            self._print_real_time_report(latest, latest_time, analysis)

            return analysis

        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return {"error": str(e)}

    def get_options_data(self, expiration_days: int = 30) -> Dict[str, Any]:
        """获取SQQQ期权数据"""
        try:
            print("🎯 正在获取SQQQ期权数据...")

            if not self.app:
                raise RuntimeError("工具未初始化")

            # 获取股票合约
            stock_contract = self.app.ib_adapter.data_provider.ib_client.get_stock_contract('SQQQ')
            if not stock_contract:
                return {"error": "无法获取SQQQ合约"}

            # 获取期权链
            option_data = self.app.ib_adapter.data_provider.ib_client.get_option_chain(
                stock_contract, expiration_days
            )

            if option_data:
                self._print_options_report(option_data)
                return option_data
            else:
                return {"error": "无法获取期权数据"}

        except Exception as e:
            logger.error(f"获取期权数据失败: {e}")
            return {"error": str(e)}

    def run_strategy_analysis(self, days: int = 30) -> Dict[str, Any]:
        """运行完整策略分析"""
        try:
            print("🎯 正在进行SQQQ三引擎策略分析...")

            if not self.analyzer:
                raise RuntimeError("分析器未初始化")

            # 获取完整分析
            analysis = self.analyzer.get_market_analysis(days)

            if "error" in analysis:
                print(f"❌ 分析失败: {analysis['error']}")
                return analysis

            # 生成并打印报告
            report = self.analyzer.generate_report(analysis)
            print(report)

            return analysis

        except Exception as e:
            logger.error(f"策略分析失败: {e}")
            return {"error": str(e)}

    def _print_real_time_report(self, latest: Any, latest_time: datetime, analysis: Dict[str, Any]):
        """打印实时数据报告"""
        print("\n" + "="*60)
        print(f"📈 SQQQ 实时数据报告")
        print("="*60)

        print(f"🕒 数据时间: {latest_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💰 当前价格: ${latest['close']:.2f}")

        if len(latest) > 1:
            print(f"📊 今日表现:")
            print(f"   开盘: ${latest['open']:.2f}")
            print(f"   最高: ${latest['high']:.2f}")
            print(f"   最低: ${latest['low']:.2f}")
            print(f"   成交量: {latest['volume']:,}")

        # 技术指标
        tech = analysis.get('technical_analysis', {})
        if tech:
            print(f"\n🔍 技术指标:")
            rsi = tech.get('rsi_14')
            rsi_status = tech.get('rsi_status', 'unknown')
            print(f"   RSI(14): {rsi:.1f} ({rsi_status})")

            sma = tech.get('sma_20')
            price_vs_sma = tech.get('price_vs_sma', 'unknown')
            print(f"   SMA(20): ${sma:.2f} (价格{price_vs_sma}均线)")

        # 策略信号
        signals = analysis.get('strategy_signals', {})
        if signals:
            print(f"\n🎯 三引擎策略状态:")

            engine1 = signals.get('engine_1_building', {})
            if engine1.get('active'):
                print(f"   🔥 引擎一: 🟢 建仓激活")
                print(f"      💡 {engine1.get('action', '')}")
            else:
                print(f"   🔥 引擎一: 🔴 待机")

            engine2 = signals.get('engine_2_annihilation', {})
            if engine2.get('active'):
                print(f"   ⚡ 引擎二: 🟢 成本湮灭激活")
                print(f"      💡 {engine2.get('action', '')}")
            else:
                print(f"   ⚡ 引擎二: 🔴 待机")

            engine3 = signals.get('engine_3_risk', {})
            risk_level = engine3.get('level', 'unknown')
            risk_color = {"low": "🟢", "medium": "🟡", "high": "🔴"}.get(risk_level, "⚪")
            print(f"   🛡️ 引擎三: {risk_color} 风控监控 ({risk_level}风险)")

            overall = signals.get('overall_recommendation', '')
            print(f"\n💡 整体建议: {overall}")

    def _print_options_report(self, option_data: Dict[str, Any]):
        """打印期权数据报告"""
        print("\n" + "="*80)
        print(f"🎯 SQQQ期权链分析")
        print("="*80)

        stock_price = option_data.get('stock_price', 0)
        expiration = option_data.get('expiration', '')

        print(f"📊 标的价格: ${stock_price:.2f}")
        print(f"📅 到期日: {expiration}")

        # Put期权
        puts = option_data.get('puts', [])
        if puts:
            print(f"\n📉 Put期权 (共{len(puts)}个):")
            print("-"*80)
            print(f"{'行权价':>8} {'Bid':>8} {'Ask':>8} {'Mid':>8} {'Delta':>8} {'IV':>8} {'状态'}")
            print("-"*80)

            for put in sorted(puts, key=lambda x: x['strike']):
                strike = put['strike']
                bid = put.get('bid', 0) or 0
                ask = put.get('ask', 0) or 0
                mid = (bid + ask) / 2 if bid and ask else 0
                delta = put.get('delta', 0) or 0
                iv = put.get('impliedVol', 0) or 0

                if strike > stock_price:
                    status = "ITM 🟢"
                elif abs(strike - stock_price) / stock_price < 0.05:
                    status = "ATM ⚪"
                else:
                    status = "OTM 🔴"

                print(f"${strike:7.2f} ${bid:7.2f} ${ask:7.2f} ${mid:7.2f} {delta:7.3f} {iv:6.1%} {status}")

        # Call期权
        calls = option_data.get('calls', [])
        if calls:
            print(f"\n📈 Call期权 (共{len(calls)}个):")
            print("-"*80)
            print(f"{'行权价':>8} {'Bid':>8} {'Ask':>8} {'Mid':>8} {'Delta':>8} {'IV':>8} {'状态'}")
            print("-"*80)

            for call in sorted(calls, key=lambda x: x['strike']):
                strike = call['strike']
                bid = call.get('bid', 0) or 0
                ask = call.get('ask', 0) or 0
                mid = (bid + ask) / 2 if bid and ask else 0
                delta = call.get('delta', 0) or 0
                iv = call.get('impliedVol', 0) or 0

                if strike < stock_price:
                    status = "ITM 🟢"
                elif abs(strike - stock_price) / stock_price < 0.05:
                    status = "ATM ⚪"
                else:
                    status = "OTM 🔴"

                print(f"${strike:7.2f} ${bid:7.2f} ${ask:7.2f} ${mid:7.2f} {delta:7.3f} {iv:6.1%} {status}")

    def cleanup(self):
        """清理资源"""
        if self.app:
            self.app.cleanup()


def main():
    """命令行工具入口"""
    import argparse

    parser = argparse.ArgumentParser(description='SQQQ策略工具')
    parser.add_argument('command', choices=['data', 'options', 'analysis'], help='工具命令')
    parser.add_argument('--mode', choices=['paper', 'live'], default='paper', help='交易模式')
    parser.add_argument('--days', type=int, default=5, help='数据天数')
    parser.add_argument('--expiration', type=int, default=30, help='期权到期天数')

    args = parser.parse_args()

    # 创建工具实例
    tools = SQQQTools(args.mode)

    try:
        if not tools.initialize():
            print("❌ 工具初始化失败")
            return

        if args.command == 'data':
            tools.get_real_time_data(args.days)
        elif args.command == 'options':
            tools.get_options_data(args.expiration)
        elif args.command == 'analysis':
            tools.run_strategy_analysis(args.days)

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"❌ 工具执行失败: {e}")
    finally:
        tools.cleanup()


if __name__ == "__main__":
    main()