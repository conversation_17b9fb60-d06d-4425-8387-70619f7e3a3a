"""
SQQQ策略配置管理
"""
from typing import Dict, Any
from dataclasses import dataclass


@dataclass
class SQQQConfig:
    """SQQQ策略配置类"""

    # 基础配置
    symbol: str = 'SQQQ'
    exchange: str = 'SMART'
    currency: str = 'USD'

    # 技术指标参数
    rsi_period: int = 14
    sma_period: int = 20
    rsi_oversold: float = 30.0
    rsi_overbought: float = 70.0

    # 期权策略参数
    target_delta_put: float = -0.3    # Put期权目标Delta
    target_delta_call: float = 0.3    # Call期权目标Delta
    expiration_days: int = 30         # 期权到期天数

    # 风险管理参数
    stop_loss_pct: float = 0.10       # 10%止损
    max_position_size: int = 1000     # 最大仓位
    position_size_pct: float = 0.1    # 每次交易占总资金比例

    # 策略行为参数
    min_premium: float = 0.10         # 最小期权费要求
    max_trades_per_day: int = 3       # 每日最大交易次数
    cooling_period_hours: int = 4     # 交易冷却期

    def __init__(self, config_dict: Dict[str, Any] = None):
        """从字典初始化配置"""
        if config_dict:
            for key, value in config_dict.items():
                if hasattr(self, key):
                    setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'symbol': self.symbol,
            'exchange': self.exchange,
            'currency': self.currency,
            'rsi_period': self.rsi_period,
            'sma_period': self.sma_period,
            'rsi_oversold': self.rsi_oversold,
            'rsi_overbought': self.rsi_overbought,
            'target_delta_put': self.target_delta_put,
            'target_delta_call': self.target_delta_call,
            'expiration_days': self.expiration_days,
            'stop_loss_pct': self.stop_loss_pct,
            'max_position_size': self.max_position_size,
            'position_size_pct': self.position_size_pct,
            'min_premium': self.min_premium,
            'max_trades_per_day': self.max_trades_per_day,
            'cooling_period_hours': self.cooling_period_hours
        }

    def validate(self) -> bool:
        """验证配置有效性"""
        checks = [
            (0 < self.rsi_oversold < 50, "RSI超卖阈值应在0-50之间"),
            (50 < self.rsi_overbought < 100, "RSI超买阈值应在50-100之间"),
            (self.rsi_oversold < self.rsi_overbought, "RSI超卖应小于超买阈值"),
            (-1 < self.target_delta_put < 0, "Put Delta应在-1到0之间"),
            (0 < self.target_delta_call < 1, "Call Delta应在0到1之间"),
            (0 < self.stop_loss_pct < 1, "止损比例应在0-100%之间"),
            (self.max_position_size > 0, "最大仓位应大于0"),
            (0 < self.position_size_pct <= 1, "仓位比例应在0-100%之间")
        ]

        for check, message in checks:
            if not check:
                raise ValueError(f"配置验证失败: {message}")

        return True

    def __str__(self) -> str:
        """字符串表示"""
        return f"SQQQConfig(symbol={self.symbol}, rsi={self.rsi_oversold}-{self.rsi_overbought}, delta_put={self.target_delta_put}, delta_call={self.target_delta_call})"


# 预定义配置模板
CONSERVATIVE_CONFIG = {
    'rsi_oversold': 25,
    'rsi_overbought': 75,
    'stop_loss_pct': 0.08,
    'position_size_pct': 0.05,
    'target_delta_put': -0.2,
    'target_delta_call': 0.2
}

AGGRESSIVE_CONFIG = {
    'rsi_oversold': 35,
    'rsi_overbought': 65,
    'stop_loss_pct': 0.15,
    'position_size_pct': 0.15,
    'target_delta_put': -0.4,
    'target_delta_call': 0.4
}

BALANCED_CONFIG = {
    'rsi_oversold': 30,
    'rsi_overbought': 70,
    'stop_loss_pct': 0.10,
    'position_size_pct': 0.10,
    'target_delta_put': -0.3,
    'target_delta_call': 0.3
}