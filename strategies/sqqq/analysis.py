"""
SQQQ策略分析工具
提供策略性能分析、数据获取和期权分析功能
"""
import logging
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class SQQQAnalyzer:
    """SQQQ策略分析器"""

    def __init__(self, data_provider=None):
        self.data_provider = data_provider
        self.symbol = 'SQQQ'

    def get_market_analysis(self, days: int = 30) -> Dict[str, Any]:
        """获取市场分析"""
        try:
            if not self.data_provider:
                raise ValueError("需要数据提供者")

            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # 获取历史数据
            data = self.data_provider.get_historical_data(self.symbol, start_date, end_date)

            if data.empty:
                return {"error": "无法获取历史数据"}

            # 计算技术指标
            analysis = self._calculate_technical_analysis(data)

            # 策略信号分析
            signals = self._analyze_strategy_signals(data)

            return {
                "symbol": self.symbol,
                "period": f"{days}天",
                "data_points": len(data),
                "latest_price": float(data['close'].iloc[-1]),
                "price_change": float(data['close'].iloc[-1] - data['close'].iloc[0]),
                "price_change_pct": float((data['close'].iloc[-1] - data['close'].iloc[0]) / data['close'].iloc[0] * 100),
                "technical_analysis": analysis,
                "strategy_signals": signals,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"市场分析失败: {e}")
            return {"error": str(e)}

    def _calculate_technical_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算技术分析指标"""
        try:
            prices = data['close']

            # RSI
            rsi = self._calculate_rsi(prices, 14)
            current_rsi = float(rsi.iloc[-1]) if not rsi.empty else None

            # SMA
            sma_20 = prices.rolling(window=20).mean()
            current_sma = float(sma_20.iloc[-1]) if not sma_20.empty else None

            # EMA
            ema_12 = prices.ewm(span=12).mean()
            ema_26 = prices.ewm(span=26).mean()
            current_ema_12 = float(ema_12.iloc[-1]) if not ema_12.empty else None
            current_ema_26 = float(ema_26.iloc[-1]) if not ema_26.empty else None

            # MACD
            macd_line = ema_12 - ema_26
            signal_line = macd_line.ewm(span=9).mean()
            histogram = macd_line - signal_line

            # 布林带
            bb_std = prices.rolling(window=20).std()
            bb_upper = sma_20 + (bb_std * 2)
            bb_lower = sma_20 - (bb_std * 2)

            # 波动率
            returns = prices.pct_change().dropna()
            volatility = float(returns.std() * (252 ** 0.5) * 100)  # 年化波动率

            # 当前价格位置
            current_price = float(prices.iloc[-1])
            bb_position = None
            if not bb_upper.empty and not bb_lower.empty:
                bb_range = float(bb_upper.iloc[-1] - bb_lower.iloc[-1])
                bb_position = float((current_price - bb_lower.iloc[-1]) / bb_range * 100) if bb_range > 0 else 50

            return {
                "rsi_14": current_rsi,
                "rsi_status": self._get_rsi_status(current_rsi),
                "sma_20": current_sma,
                "price_vs_sma": "above" if current_price > current_sma else "below",
                "ema_12": current_ema_12,
                "ema_26": current_ema_26,
                "macd": {
                    "line": float(macd_line.iloc[-1]) if not macd_line.empty else None,
                    "signal": float(signal_line.iloc[-1]) if not signal_line.empty else None,
                    "histogram": float(histogram.iloc[-1]) if not histogram.empty else None
                },
                "bollinger_bands": {
                    "upper": float(bb_upper.iloc[-1]) if not bb_upper.empty else None,
                    "lower": float(bb_lower.iloc[-1]) if not bb_lower.empty else None,
                    "position_pct": bb_position
                },
                "volatility_annual_pct": volatility,
                "volume_avg_20d": float(data['volume'].rolling(window=20).mean().iloc[-1]) if 'volume' in data.columns else None
            }

        except Exception as e:
            logger.error(f"技术分析计算失败: {e}")
            return {}

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss.replace(0, float('inf'))
        return 100 - (100 / (1 + rs))

    def _get_rsi_status(self, rsi: float) -> str:
        """获取RSI状态"""
        if rsi is None:
            return "unknown"
        elif rsi < 30:
            return "oversold"
        elif rsi > 70:
            return "overbought"
        elif rsi < 40:
            return "weak"
        elif rsi > 60:
            return "strong"
        else:
            return "neutral"

    def _analyze_strategy_signals(self, data: pd.DataFrame) -> Dict[str, Any]:
        """分析策略信号"""
        try:
            prices = data['close']
            rsi = self._calculate_rsi(prices, 14)
            sma = prices.rolling(window=20).mean()

            if rsi.empty or sma.empty:
                return {}

            current_price = float(prices.iloc[-1])
            current_rsi = float(rsi.iloc[-1])
            current_sma = float(sma.iloc[-1])

            # 引擎一：建仓信号
            building_active = current_rsi < 30 and current_price < current_sma
            building_strength = max(0, (30 - current_rsi) / 20) if current_rsi < 30 else 0

            # 引擎二：成本湮灭信号
            annihilation_active = current_rsi > 70
            annihilation_strength = max(0, (current_rsi - 70) / 20) if current_rsi > 70 else 0

            # 引擎三：风控状态
            risk_level = "low"
            if current_rsi > 80 or current_rsi < 20:
                risk_level = "high"
            elif current_rsi > 75 or current_rsi < 25:
                risk_level = "medium"

            return {
                "engine_1_building": {
                    "active": building_active,
                    "strength": float(building_strength),
                    "condition": f"RSI({current_rsi:.1f})<30 AND Price({current_price:.2f})<SMA({current_sma:.2f})",
                    "action": "卖出Put期权" if building_active else "等待机会"
                },
                "engine_2_annihilation": {
                    "active": annihilation_active,
                    "strength": float(annihilation_strength),
                    "condition": f"RSI({current_rsi:.1f})>70",
                    "action": "卖出Call期权" if annihilation_active else "等待机会"
                },
                "engine_3_risk": {
                    "level": risk_level,
                    "monitoring": True,
                    "action": "持续监控"
                },
                "overall_recommendation": self._get_overall_recommendation(building_active, annihilation_active, risk_level)
            }

        except Exception as e:
            logger.error(f"策略信号分析失败: {e}")
            return {}

    def _get_overall_recommendation(self, building: bool, annihilation: bool, risk: str) -> str:
        """获取整体建议"""
        if risk == "high":
            return "高风险环境，建议观望"
        elif building:
            return "绝佳建仓机会！RSI超卖且价格低于SMA"
        elif annihilation:
            return "成本湮灭时机！RSI超买，适合卖Call"
        else:
            return "等待更好的进入时机"

    def get_options_analysis(self, expiration_days: int = 30) -> Dict[str, Any]:
        """获取期权分析"""
        try:
            if not self.data_provider:
                raise ValueError("需要数据提供者")

            # 这里需要具体的期权数据接口
            # 暂时返回模拟结构
            return {
                "symbol": self.symbol,
                "expiration_days": expiration_days,
                "analysis_timestamp": datetime.now().isoformat(),
                "note": "期权分析需要具体的期权数据接口实现"
            }

        except Exception as e:
            logger.error(f"期权分析失败: {e}")
            return {"error": str(e)}

    def generate_report(self, analysis: Dict[str, Any]) -> str:
        """生成分析报告"""
        if "error" in analysis:
            return f"❌ 分析失败: {analysis['error']}"

        report = []
        report.append(f"📊 {analysis['symbol']} 策略分析报告")
        report.append("=" * 50)

        # 基础信息
        report.append(f"📈 最新价格: ${analysis['latest_price']:.2f}")
        report.append(f"📊 价格变化: {analysis['price_change']:+.2f} ({analysis['price_change_pct']:+.1f}%)")
        report.append(f"📅 分析周期: {analysis['period']} ({analysis['data_points']} 个数据点)")

        # 技术分析
        tech = analysis.get('technical_analysis', {})
        if tech:
            report.append("\n🔍 技术分析:")
            report.append(f"   RSI(14): {tech.get('rsi_14', 0):.1f} ({tech.get('rsi_status', 'unknown')})")
            report.append(f"   SMA(20): ${tech.get('sma_20', 0):.2f} (价格{tech.get('price_vs_sma', 'unknown')}均线)")
            report.append(f"   年化波动率: {tech.get('volatility_annual_pct', 0):.1f}%")

            bb = tech.get('bollinger_bands', {})
            if bb.get('position_pct') is not None:
                report.append(f"   布林带位置: {bb['position_pct']:.0f}%")

        # 策略信号
        signals = analysis.get('strategy_signals', {})
        if signals:
            report.append("\n🎯 三引擎策略状态:")

            engine1 = signals.get('engine_1_building', {})
            status1 = "🟢 激活" if engine1.get('active') else "🔴 待机"
            report.append(f"   🔥 引擎一 [建仓]: {status1}")
            if engine1.get('active'):
                report.append(f"      💡 {engine1.get('action', '')}")

            engine2 = signals.get('engine_2_annihilation', {})
            status2 = "🟢 激活" if engine2.get('active') else "🔴 待机"
            report.append(f"   ⚡ 引擎二 [成本湮灭]: {status2}")
            if engine2.get('active'):
                report.append(f"      💡 {engine2.get('action', '')}")

            engine3 = signals.get('engine_3_risk', {})
            risk_color = {"low": "🟢", "medium": "🟡", "high": "🔴"}.get(engine3.get('level'), "⚪")
            report.append(f"   🛡️ 引擎三 [风控]: {risk_color} {engine3.get('level', 'unknown')}风险")

            overall = signals.get('overall_recommendation', '')
            report.append(f"\n💡 整体建议: {overall}")

        report.append(f"\n🕒 分析时间: {analysis.get('timestamp', '')}")

        return "\n".join(report)