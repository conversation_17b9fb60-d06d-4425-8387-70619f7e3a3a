# 快速开始指南

## 5分钟快速接入IB进行自动化交易

### 前置条件

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **IB Gateway设置**
   - 下载并安装IB Gateway或Trader Workstation (TWS)
   - 启动IB Gateway，选择模拟交易或实盘交易
   - 在Configuration -> API Settings中：
     - ✅ Enable ActiveX and Socket Clients
     - ❌ Read-Only API (取消勾选)
     - 端口设置：7497 (模拟) / 7496 (实盘)

### 方式一：使用交互式界面（推荐新手）

```bash
python run.py
```

选择模式1 (Paper Trading) 开始模拟交易。

### 方式二：使用简单示例代码

```bash
python simple_trading_example.py
```

这个示例包含：
- 连接IB Gateway
- 简单移动平均策略
- 自动交易执行
- 实时监控

### 方式三：自定义策略开发

```python
from trade.app.production_trading_app import ProductionTradingApplication, create_production_config
from strategies.sqqq import SQQQStrategy, SQQQConfig

# 创建应用
config = create_production_config('paper')
app = ProductionTradingApplication(config)
app.initialize()

# 创建策略
strategy_config = SQQQConfig({
    'rsi_oversold': 25,
    'rsi_overbought': 75
})
strategy = SQQQStrategy("My_SQQQ_Strategy", strategy_config.to_dict())

# 添加策略并启动
app.add_strategy(strategy)
app.run_live_trading()
```

## 核心概念

### 1. 架构分离
- `trade/` - 核心交易基础设施
- `strategies/` - 具体交易策略
- 框架与策略完全解耦

### 2. 三种运行模式
- **Paper Trading**: 模拟交易，真实数据，零风险
- **Live Trading**: 实盘交易，真实资金
- **Backtest**: 历史数据回测

### 3. 策略开发
继承`Strategy`基类，实现三个核心方法：
- `generate_signals()` - 生成交易信号
- `on_market_data()` - 处理市场数据
- `on_signal()` - 处理交易信号

## 常见问题

### 连接问题
```
错误: 无法连接到IB Gateway
```

**解决方案:**
1. 确保IB Gateway正在运行
2. 检查端口：模拟交易=7497，实盘交易=7496
3. 检查API设置已启用
4. 检查防火墙和网络

### 数据权限
```
错误: 数据权限不足
```

**解决方案:**
1. 登录IB账户管理
2. 订阅相应的市场数据包
3. 等待权限生效（通常几分钟）

### 策略错误
```
错误: 策略未找到
```

**解决方案:**
1. 检查策略文件导入路径
2. 确保策略类继承`Strategy`基类
3. 检查配置参数格式

## 安全提醒

1. **先用Paper Trading测试**
   - 任何新策略都应在模拟环境充分测试
   - 验证策略逻辑和风险控制

2. **设置止损**
   - 每个策略都应有止损机制
   - 控制单笔和总体风险

3. **监控资金**
   - 定期检查账户状态
   - 设置合理的仓位大小

4. **保护密钥**
   - 不要将账户信息硬编码
   - 使用环境变量管理敏感信息

## 获取帮助

- 查看日志文件：`logs/trading.log`
- 运行诊断工具：`python fix_data_permissions.py`
- 检查系统状态：使用交互式界面

## 下一步

1. 阅读完整文档：`README.md`
2. 了解架构设计：`ARCHITECTURE.md`
3. 研究SQQQ策略示例：`strategies/sqqq/README.md`
4. 开发自己的策略

---

**记住：交易有风险，投资需谨慎。本框架仅提供技术工具，不构成投资建议。**