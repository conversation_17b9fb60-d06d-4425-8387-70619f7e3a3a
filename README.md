# 专业量化交易框架

一个**生产级量化交易基础设施**，专注于与券商的稳定连接、可靠的数据获取和专业的交易执行。框架与策略完全分离，支持多种交易策略开发。

## 项目定位

**这不是一个SQQQ策略工具，而是一个完整的量化交易基础设施**

- **核心价值**: 稳定的broker连接、可靠的数据获取、专业的交易执行
- **策略分离**: SQQQ策略仅作为示例，策略与框架完全解耦
- **生产就绪**: 适用于实际交易环境的专业基础设施

## 架构特点

### 分层架构设计
```
量化交易项目
├── trade/              # 核心交易框架 (基础设施)
├── strategies/         # 交易策略集合 (业务逻辑)
├── 工具脚本            # 便捷数据工具
└── 文档               # 完整项目文档
```

### 核心框架能力 (trade/)
- **Broker连接**: Interactive Brokers集成，自动重连，健康监控
- **数据管理**: 实时数据流，历史数据缓存，市场时间感知
- **交易执行**: 订单全生命周期跟踪，执行质量监控
- **风险管理**: 仓位控制，动态止损，资金管理
- **回测引擎**: 历史数据回测，性能分析
- **监控界面**: 实时状态监控，交互式控制

### 策略示例 (strategies/sqqq/)
SQQQ反向ETF三引擎期权策略：
- **引擎一**: RSI超卖时建仓（卖Put期权）
- **引擎二**: RSI超买时成本湮灭（卖Call期权）
- **引擎三**: 持续风险监控和动态止损

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <your-repo-url>
cd ib

# 安装依赖
pip install -r requirements.txt
```

### 2. IB Gateway配置

1. **启动IB Gateway/TWS**
   - Paper Trading端口: 7497
   - Live Trading端口: 7496

2. **API设置**
   - 启用 "Enable ActiveX and Socket Clients"
   - 取消 "Read-Only API"
   - 设置端口: 7497 (Paper) / 7496 (Live)

3. **数据权限**
   - 美股实时数据包 (如需实时数据)
   - 期权数据权限 (如需期权交易)

### 3. 运行方式

#### 交互式启动 (推荐)
```bash
python run.py
```

选择运行模式：
- **Paper Trading** - 模拟交易 (推荐新手)
- **Live Trading** - 实盘交易 (需谨慎)
- **Backtest Analysis** - 历史回测分析
- **数据分析工具** - 各种数据获取和分析工具

#### 直接启动
```bash
# 模拟交易
python run.py paper

# 实盘交易 (谨慎!)
python run.py live

# 历史回测
python run.py backtest
```

## 策略使用示例

### 使用SQQQ策略
```python
from trade.app.production_trading_app import ProductionTradingApplication, create_production_config
from strategies.sqqq import SQQQStrategy, SQQQConfig

# 创建应用
config = create_production_config('paper')
app = ProductionTradingApplication(config)
app.initialize()

# 创建SQQQ策略
sqqq_config = SQQQConfig({
    'rsi_oversold': 30,
    'rsi_overbought': 70,
    'stop_loss_pct': 0.10
})
strategy = SQQQStrategy("SQQQ_Strategy", sqqq_config.to_dict())

# 添加策略并启动
app.add_strategy(strategy)
app.run_live_trading()
```

### 独立的SQQQ工具
```bash
# 获取实时数据
python strategies/sqqq/tools.py data --days 5

# 分析期权数据
python strategies/sqqq/tools.py options --expiration 30

# 完整策略分析
python strategies/sqqq/tools.py analysis --days 30
```

## 便捷工具

### 数据获取工具
```bash
# SQQQ实时数据 (简化版)
python get_sqqq.py

# SQQQ期权分析 (简化版)
python get_sqqq_options.py

# 智能数据获取 (市场时间感知)
python smart_sqqq_data.py

# 数据权限诊断
python fix_data_permissions.py
```

## 项目结构

### 核心框架 (trade/)
```
trade/
├── core/                    # 核心组件
│   ├── base.py             # 基础数据结构
│   ├── realtime_manager.py # 实时数据管理
│   ├── execution_manager.py # 交易执行管理
│   └── config.py           # 框架配置
├── adapters/               # 券商适配器
│   └── ib_adapter.py       # IB集成 (增强版)
├── data/                   # 数据管理
├── engine/                 # 交易引擎
├── backtest/              # 回测引擎
└── app/                   # 应用控制器
```

### 策略集合 (strategies/)
```
strategies/
└── sqqq/                  # SQQQ策略示例
    ├── strategy.py        # 策略核心实现
    ├── config.py          # 策略配置
    ├── analysis.py        # 市场分析
    ├── tools.py           # 独立工具
    └── README.md          # 策略文档
```

## 风险管理

### 内置风险控制
- **仓位限制**: 单个仓位和组合风险限制
- **资金检查**: 可用资金验证
- **订单验证**: 价格合理性检查
- **动态止损**: 自动止损保护
- **超时保护**: 订单超时自动取消

### 分层安全机制
- **框架级**: 连接稳定性、数据质量监控
- **策略级**: 信号验证、交易逻辑检查
- **应用级**: 用户确认、权限控制

## 开发和扩展

### 添加新策略
1. 在 `strategies/` 创建新目录
2. 实现 `Strategy` 接口
3. 创建配置和分析工具
4. 编写策略文档

### 添加新Broker
1. 在 `trade/adapters/` 实现新适配器
2. 实现 `DataProvider` 和 `Broker` 接口
3. 集成到应用配置

### 框架增强
- 扩展数据源
- 改进风险管理
- 优化执行算法
- 增加监控功能

## 文档

- **[架构文档](ARCHITECTURE.md)** - 详细的系统架构说明
- **[生产级基础设施](PRODUCTION_INFRASTRUCTURE.md)** - 生产环境部署指南
- **[SQQQ策略文档](strategies/sqqq/README.md)** - SQQQ策略详细说明

## 重要提醒

1. **这是交易基础设施，不是投资建议**
2. **SQQQ策略仅为示例，实际使用需充分了解风险**
3. **实盘交易前请在Paper模式充分测试**
4. **确保IB账户有相应的数据和交易权限**
5. **合理控制仓位，做好风险管理**

## 支持

- **日志分析**: 查看 `logs/` 目录
- **连接诊断**: 运行 `python fix_data_permissions.py`
- **配置问题**: 检查 `trade/core/config.py`
- **监控界面**: 使用交互式命令查看系统状态

## 核心价值

这是一个**专业的量化交易基础设施**，提供：
- 🔗 稳定可靠的broker连接
- 📊 高质量的数据获取和管理
- ⚡ 专业的交易执行和监控
- 🛡️ 完善的风险管理机制
- 🏗️ 可扩展的架构设计

**策略只是展示框架能力的示例，真正的价值在于提供了构建任何量化交易策略的坚实基础。**