"""
量化交易框架使用示例
"""
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO)

# 示例1: 回测SQQQ策略
def run_backtest_example():
    """回测示例"""
    from trade.app.trading_app import TradingApplication, create_app_config

    print("=== SQQQ策略回测示例 ===")

    # 创建回测配置
    config = create_app_config(mode='backtest')
    config.update({
        'initial_capital': 100000,  # 初始资金10万
        'commission': 0.001,        # 手续费0.1%
        'log_level': 'INFO'
    })

    # 创建应用
    app = TradingApplication(config)

    try:
        # 初始化
        app.initialize()

        # 运行回测 (最近一年)
        start_date = datetime.now() - timedelta(days=365)
        end_date = datetime.now()

        result, metrics = app.run_backtest('sqqq_main', start_date, end_date)

        print("\n回测完成!")
        print(f"总收益率: {metrics['Total Return']:.2%}")
        print(f"年化收益率: {metrics['Annual Return']:.2%}")
        print(f"夏普比率: {metrics['Sharpe Ratio']:.3f}")
        print(f"最大回撤: {metrics['Max Drawdown']:.2%}")

    except Exception as e:
        print(f"回测失败: {e}")
    finally:
        app.stop()


# 示例2: 实时交易
def run_live_trading_example():
    """实时交易示例"""
    from trade.app.trading_app import TradingApplication, create_app_config

    print("=== SQQQ策略实时交易示例 ===")

    # 创建实时交易配置
    config = create_app_config(mode='live')  # 或 'paper' 用于模拟交易

    # 创建应用
    app = TradingApplication(config)

    try:
        # 初始化 (连接到IB Gateway)
        app.initialize()

        print("开始实时交易...")
        print("按 Ctrl+C 停止")

        # 运行实时交易
        app.run_live_trading()

    except KeyboardInterrupt:
        print("\n用户停止交易")
    except Exception as e:
        print(f"实时交易失败: {e}")
    finally:
        app.stop()


# 示例3: 自定义策略
def create_custom_strategy_example():
    """自定义策略示例"""
    from trade.core.base import Strategy, Signal, MarketData
    from typing import List

    class SimpleMAStrategy(Strategy):
        """简单移动平均策略示例"""

        def __init__(self, fast_period=10, slow_period=20):
            super().__init__("Simple_MA_Strategy")
            self.fast_period = fast_period
            self.slow_period = slow_period
            self.price_history = []

        def generate_signals(self, market_data: MarketData) -> List[Signal]:
            signals = []

            # 更新价格历史
            self.price_history.append(market_data.close)
            if len(self.price_history) > self.slow_period:
                self.price_history.pop(0)

            # 需要足够的数据
            if len(self.price_history) < self.slow_period:
                return signals

            # 计算移动平均
            fast_ma = sum(self.price_history[-self.fast_period:]) / self.fast_period
            slow_ma = sum(self.price_history) / len(self.price_history)

            # 生成信号
            if fast_ma > slow_ma:
                signal = Signal(
                    symbol=market_data.symbol,
                    signal_type='BUY',
                    strength=0.8,
                    timestamp=market_data.timestamp,
                    reason=f"快线({fast_ma:.2f}) > 慢线({slow_ma:.2f})"
                )
                signals.append(signal)

            elif fast_ma < slow_ma:
                signal = Signal(
                    symbol=market_data.symbol,
                    signal_type='SELL',
                    strength=0.8,
                    timestamp=market_data.timestamp,
                    reason=f"快线({fast_ma:.2f}) < 慢线({slow_ma:.2f})"
                )
                signals.append(signal)

            return signals

        def on_market_data(self, data: MarketData) -> None:
            signals = self.generate_signals(data)
            for signal in signals:
                print(f"策略信号: {signal.signal_type} - {signal.reason}")

        def on_signal(self, signal: Signal) -> None:
            print(f"执行信号: {signal.signal_type}")

    return SimpleMAStrategy()


def main():
    """主函数"""
    print("量化交易框架使用示例")
    print("请选择运行模式:")
    print("1. 回测示例")
    print("2. 实时交易示例")
    print("3. 自定义策略示例")

    try:
        choice = input("请选择 (1-3): ").strip()

        if choice == '1':
            run_backtest_example()
        elif choice == '2':
            run_live_trading_example()
        elif choice == '3':
            strategy = create_custom_strategy_example()
            print(f"创建自定义策略: {strategy.name}")

            # 模拟市场数据测试
            from trade.core.base import MarketData
            test_data = MarketData(
                symbol='TEST',
                timestamp=datetime.now(),
                open=100, high=102, low=99, close=101, volume=10000
            )
            strategy.on_market_data(test_data)
        else:
            print("无效选择")

    except Exception as e:
        print(f"运行失败: {e}")


if __name__ == "__main__":
    main()