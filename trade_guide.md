# 🚀 专业量化交易框架使用指南 (trade)

## 📊 框架架构概览

```
┌─────────────────────────────────────────┐
│           Trading Application           │  <- 应用层
├─────────────────────────────────────────┤
│           Strategy Layer                │  <- 策略层
├─────────────────────────────────────────┤
│           Trading Engine                │  <- 交易引擎
├─────────────────────────────────────────┤
│        Risk Management                  │  <- 风控层
├─────────────────────────────────────────┤
│        Data Management                  │  <- 数据管理
├─────────────────────────────────────────┤
│        Broker Adapter                   │  <- 券商适配
└─────────────────────────────────────────┘
```

## 🏗️ 核心组件

### 1. 数据层 (Data Layer)
- **HistoricalDataManager**: 历史数据管理，支持缓存和持久化
- **RealtimeDataManager**: 实时数据流处理和分发
- **DataStore**: 统一数据存储接口 (SQLite)
- **TechnicalIndicators**: 技术指标计算库

### 2. 策略层 (Strategy Layer)
- **BaseStrategy**: 策略抽象基类
- **SQQQStrategy**: SQQQ成本湮灭轮动策略
- **Signal**: 标准化信号结构

### 3. 交易引擎 (Trading Engine)
- **TradingEngine**: 交易执行核心
- **OrderManager**: 订单生命周期管理
- **PositionManager**: 仓位管理和计算
- **RiskManager**: 风险控制和仓位管理

### 4. 回测引擎 (Backtesting)
- **BacktestEngine**: 历史数据回测
- **SimulatedBroker**: 模拟经纪商
- **PerformanceAnalyzer**: 绩效分析和报告

### 5. 适配器层 (Adapters)
- **IBDataProvider**: IB数据接口适配
- **IBBroker**: IB交易接口适配
- **IBFrameworkAdapter**: 统一IB接口封装

## 🚀 快速开始

### 安装依赖
```bash
# 确保已安装框架依赖
pip install -r requirements.txt
```

### 1. 运行SQQQ策略回测
```python
from trade.app.trading_app import TradingApplication, create_app_config
from datetime import datetime, timedelta

# 创建回测配置
config = create_app_config(mode='backtest')
app = TradingApplication(config)

# 初始化并运行回测
app.initialize()
start_date = datetime.now() - timedelta(days=365)
end_date = datetime.now()
result, metrics = app.run_backtest('sqqq_main', start_date, end_date)
```

### 2. 运行实时交易
```python
# 创建实时交易应用
config = create_app_config(mode='live')  # 或 'paper'
app = TradingApplication(config)

# 启动实时交易
app.initialize()
app.run_live_trading()
```

### 3. 创建自定义策略
```python
from trade.core.base import Strategy, Signal, MarketData

class MyStrategy(Strategy):
    def generate_signals(self, market_data: MarketData):
        # 实现您的策略逻辑
        return []

    def on_market_data(self, data: MarketData):
        # 处理实时数据
        pass
```

## 📈 SQQQ策略详解

### 三大引擎架构
1. **引擎一：建仓引擎**
   - 条件：RSI < 30 且价格 < SMA
   - 动作：卖出Put期权收取权利金

2. **引擎二：成本湮灭**
   - 条件：持有股票且RSI > 70
   - 动作：卖出Covered Call降低成本

3. **引擎三：风控引擎**
   - 条件：持仓亏损超过阈值
   - 动作：止损保护资金

### 策略配置
```python
sqqq_config = {
    'symbol': 'SQQQ',
    'rsi_period': 14,
    'sma_period': 20,
    'rsi_oversold': 30,
    'rsi_overbought': 70,
    'target_delta_put': -0.3,
    'target_delta_call': 0.3
}
```

## 🛡️ 风险管理

### 风控参数
```python
risk_config = {
    'max_position_size': 1000,      # 最大单仓股数
    'max_portfolio_risk': 0.2,      # 组合最大风险20%
    'max_single_position_risk': 0.1, # 单仓最大风险10%
    'stop_loss_pct': 0.05           # 止损比例5%
}
```

### 风控功能
- **仓位控制**: 基于Kelly公式的动态仓位计算
- **风险预算**: 组合和单仓风险限制
- **止损保护**: 自动触发止损机制
- **保证金管理**: 实时监控保证金使用率

## 📊 回测功能

### 回测配置
```python
backtest_config = BacktestConfig(
    initial_capital=100000,     # 初始资金
    commission_per_share=0.001, # 手续费
    slippage_pct=0.0001,       # 滑点
    start_date=start_date,
    end_date=end_date
)
```

### 绩效指标
- **收益指标**: 总收益率、年化收益率
- **风险指标**: 最大回撤、波动率、夏普比率
- **交易指标**: 胜率、盈亏比、交易次数
- **基准比较**: Alpha、Beta、信息比率

## 🔧 配置说明

### 应用配置文件
```python
app_config = {
    'mode': 'live',             # live/backtest/paper
    'initial_capital': 100000,
    'log_level': 'INFO',
    'strategies': [{
        'type': 'sqqq',
        'name': 'sqqq_main',
        'params': sqqq_config
    }]
}
```

### IB Gateway配置
1. **连接设置**
   - Paper Trading端口: 7497
   - Live Trading端口: 7496
   - API连接: 必须启用

2. **权限设置**
   - 取消"Read-Only API"
   - 允许客户端ID连接
   - 启用期权交易权限

## 📁 文件结构

```
trade/
├── core/           # 核心基础类
│   ├── base.py
│   ├── config.py
│   └── utils.py
├── data/           # 数据管理
│   └── data_manager.py
├── engine/         # 交易引擎
│   └── trading_engine.py
├── backtest/       # 回测引擎
│   └── backtest_engine.py
├── adapters/       # 接口适配
│   └── ib_adapter.py
├── strategies/     # 策略实现
│   └── sqqq_strategy.py
└── app/           # 应用程序
    └── trading_app.py
```

## 🔍 使用示例

### 命令行运行
```bash
# 回测模式
python trade/app/trading_app.py backtest

# 实时交易模式
python trade/app/trading_app.py live

# Paper交易模式
python trade/app/trading_app.py paper
```

### 程序化调用
```python
# 查看完整示例
python trade_example.py
```

## 🚨 注意事项

1. **安全第一**: 请务必在Paper账户充分测试
2. **数据权限**: 确保有相应的市场数据订阅
3. **风控设置**: 根据资金规模合理设置风控参数
4. **监控日志**: 实时监控策略运行状态
5. **定期回测**: 持续验证策略有效性

## 🔮 扩展开发

### 添加新策略
1. 继承`Strategy`基类
2. 实现`generate_signals`方法
3. 在配置中注册策略

### 添加新指标
1. 在`TechnicalIndicators`中添加计算方法
2. 在策略中调用新指标

### 自定义风控
1. 继承`RiskManager`
2. 重写风控逻辑
3. 在交易引擎中使用

---

**祝您交易顺利！** 📈💰